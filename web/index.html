<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boce Service 管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#"><i class="bi bi-gear-fill"></i> Boce Service 管理平台</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" onclick="showPage('items')">
                            <i class="bi bi-list-check"></i> Item管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('nodes')">
                            <i class="bi bi-hdd-network"></i> Node管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('dns')">
                            <i class="bi bi-globe"></i> DNS管理
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Item管理页面 -->
        <div id="items-page" class="page-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-list-check"></i> Item管理</h2>
                <button class="btn btn-primary" onclick="showAddItemModal()">
                    <i class="bi bi-plus-circle"></i> 添加Item
                </button>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="items-table">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>名称</th>
                                    <th>目标</th>
                                    <th>方法</th>
                                    <th>状态</th>
                                    <th>所有者</th>
                                    <th>间隔(秒)</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="items-tbody">
                                <!-- 动态加载数据 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Node管理页面 -->
        <div id="nodes-page" class="page-content" style="display: none;">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-hdd-network"></i> Node管理</h2>
                <div>
                    <button class="btn btn-success" onclick="pushAllConfigs()">
                        <i class="bi bi-cloud-upload"></i> 推送所有配置
                    </button>
                    <button class="btn btn-info" onclick="refreshNodes()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                </div>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="nodes-table">
                            <thead class="table-dark">
                                <tr>
                                    <th>IP地址</th>
                                    <th>位置</th>
                                    <th>ISP</th>
                                    <th>DNS服务器</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="nodes-tbody">
                                <!-- 动态加载数据 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- DNS管理页面 -->
        <div id="dns-page" class="page-content" style="display: none;">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-globe"></i> DNS管理</h2>
                <div>
                    <button class="btn btn-primary" onclick="showAddDnsModal()">
                        <i class="bi bi-plus-circle"></i> 添加DNS
                    </button>
                    <button class="btn btn-warning" onclick="reloadDnsList()">
                        <i class="bi bi-arrow-clockwise"></i> 重新加载DNS列表
                    </button>
                </div>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="dns-table">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>IP地址</th>
                                    <th>位置</th>
                                    <th>ISP</th>
                                    <th>所有者</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="dns-tbody">
                                <!-- 动态加载数据 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Item添加/编辑模态框 -->
    <div class="modal fade" id="itemModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="itemModalTitle">添加Item</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="itemForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="itemTarget" class="form-label">目标 *</label>
                                    <input type="text" class="form-control" id="itemTarget" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="itemMethod" class="form-label">方法 *</label>
                                    <select class="form-select" id="itemMethod" required onchange="updateItemDataFields()">
                                        <option value="">选择方法</option>
                                        <option value="HTTP_GET">HTTP_GET</option>
                                        <option value="HTTP_POST">HTTP_POST</option>
                                        <option value="TCP">TCP</option>
                                        <option value="ICMP">ICMP</option>
                                        <option value="DNS">DNS</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="itemOwner" class="form-label">所有者</label>
                                    <input type="text" class="form-control" id="itemOwner">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="itemInterval" class="form-label">间隔(秒)</label>
                                    <input type="number" class="form-control" id="itemInterval" value="30">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="itemStatus" class="form-label">状态</label>
                            <select class="form-select" id="itemStatus">
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                        
                        <!-- 动态配置字段 -->
                        <div id="itemDataFields"></div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveItem()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- DNS添加模态框 -->
    <div class="modal fade" id="dnsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加DNS</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="dnsForm">
                        <div class="mb-3">
                            <label for="dnsIpAddr" class="form-label">IP地址 *</label>
                            <input type="text" class="form-control" id="dnsIpAddr" required>
                        </div>
                        <div class="mb-3">
                            <label for="dnsOwner" class="form-label">所有者</label>
                            <input type="text" class="form-control" id="dnsOwner">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveDns()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载中模态框 -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">处理中...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Node详情模态框 -->
    <div class="modal fade" id="nodeDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Node详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="nodeDetailContent">
                    <!-- 动态加载内容 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 确认操作模态框 -->
    <div class="modal fade" id="confirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmModalTitle">确认操作</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="confirmModalBody">
                    确定要执行此操作吗？
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmModalBtn">确定</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
