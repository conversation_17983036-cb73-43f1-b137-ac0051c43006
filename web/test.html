<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        button { margin: 5px; padding: 8px 15px; }
        .result { margin-top: 10px; padding: 10px; background: #f5f5f5; white-space: pre-wrap; }
        .error { background: #ffe6e6; }
        .success { background: #e6ffe6; }
    </style>
</head>
<body>
    <h1>Boce Service API 测试</h1>
    
    <div class="test-section">
        <h3>1. Item 管理测试</h3>
        <button onclick="testGetItems()">获取 Items</button>
        <button onclick="testCreateItem()">创建测试 Item</button>
        <div id="item-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Node 管理测试</h3>
        <button onclick="testGetNodes()">获取 Nodes</button>
        <button onclick="testGetConnectedClients()">获取连接的客户端</button>
        <button onclick="testNodeStatus()">测试节点状态逻辑</button>
        <div id="node-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. DNS 管理测试</h3>
        <button onclick="testGetDNS()">获取 DNS 列表</button>
        <button onclick="testCreateDNS()">创建测试 DNS</button>
        <div id="dns-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. WebSocket 推送测试</h3>
        <button onclick="testPushAllConfigs()">推送所有配置</button>
        <button onclick="testPushToNode()">推送到指定节点</button>
        <button onclick="testReloadBlackbox()">重载 Blackbox 配置</button>
        <div id="ws-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>5. 配置生成测试</h3>
        <button onclick="testGenerateBlackboxConfig()">生成 Blackbox 配置</button>
        <button onclick="testGenerateVMAgentConfig()">生成 VMAgent 配置</button>
        <button onclick="testGenerateAllConfigs()">生成所有配置</button>
        <div id="config-result" class="result"></div>
    </div>

    <script>
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return { success: true, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            if (result.success) {
                element.className = 'result success';
                element.textContent = JSON.stringify(result.data, null, 2);
            } else {
                element.className = 'result error';
                element.textContent = 'Error: ' + result.error;
            }
        }

        // Item 测试
        async function testGetItems() {
            const result = await apiRequest('/api/items');
            displayResult('item-result', result);
        }

        async function testCreateItem() {
            const testItem = {
                target: 'https://example.com',
                method: 'HTTP_GET',
                owner: 'test',
                interval: 30,
                status: 1,
                item_data: JSON.stringify({
                    url: 'https://example.com',
                    requestMethod: 'GET',
                    headers: {}
                })
            };
            
            const result = await apiRequest('/api/items', {
                method: 'POST',
                body: JSON.stringify(testItem)
            });
            displayResult('item-result', result);
        }

        // Node 测试
        async function testGetNodes() {
            const result = await apiRequest('/api/nodes');
            displayResult('node-result', result);
        }

        async function testGetConnectedClients() {
            const result = await apiRequest('/ws/api/clients');
            displayResult('node-result', result);
        }

        // 测试节点状态逻辑
        async function testNodeStatus() {
            try {
                // 获取节点列表
                const nodesResult = await apiRequest('/api/nodes');
                const nodes = nodesResult.data || [];

                // 获取连接的客户端
                const clientsResult = await apiRequest('/ws/api/clients');
                const clients = clientsResult.clients || [];

                // 分析状态
                const statusAnalysis = nodes.map(node => {
                    const isConnected = clients.some(client => client.client_ip === node.ip_addr);
                    const isEnabled = node.status === 1;

                    let statusText = '';
                    if (isConnected && isEnabled) {
                        statusText = '在线';
                    } else if (isConnected && !isEnabled) {
                        statusText = '已连接(禁用)';
                    } else if (!isConnected && isEnabled) {
                        statusText = '离线';
                    } else {
                        statusText = '离线(禁用)';
                    }

                    return {
                        ip: node.ip_addr,
                        connected: isConnected,
                        enabled: isEnabled,
                        status: statusText
                    };
                });

                displayResult('node-result', { success: true, data: { nodes, clients, statusAnalysis } });
            } catch (error) {
                displayResult('node-result', { success: false, error: error.message });
            }
        }

        // DNS 测试
        async function testGetDNS() {
            const result = await apiRequest('/api/dns');
            displayResult('dns-result', result);
        }

        async function testCreateDNS() {
            const testDNS = {
                ip_addr: '*******',
                owner: 'test'
            };
            
            const result = await apiRequest('/api/dns', {
                method: 'POST',
                body: JSON.stringify(testDNS)
            });
            displayResult('dns-result', result);
        }

        // WebSocket 测试
        async function testPushAllConfigs() {
            const result = await apiRequest('/ws/api/push/configs', {
                method: 'POST'
            });
            displayResult('ws-result', result);
        }

        async function testPushToNode() {
            const testData = {
                target_ip: '127.0.0.1',
                message: '测试推送'
            };
            
            const result = await apiRequest('/ws/api/push/node', {
                method: 'POST',
                body: JSON.stringify(testData)
            });
            displayResult('ws-result', result);
        }

        async function testReloadBlackbox() {
            const testData = {
                target_ip: '127.0.0.1'
            };
            
            const result = await apiRequest('/ws/api/reload/blackbox', {
                method: 'POST',
                body: JSON.stringify(testData)
            });
            displayResult('ws-result', result);
        }

        // 配置生成测试
        async function testGenerateBlackboxConfig() {
            const result = await apiRequest('/api/blackbox/generate/base64?ip=127.0.0.1');
            displayResult('config-result', result);
        }

        async function testGenerateVMAgentConfig() {
            const result = await apiRequest('/api/vmagent/generate?ip=127.0.0.1');
            displayResult('config-result', result);
        }

        async function testGenerateAllConfigs() {
            const result = await apiRequest('/api/configs/generate/all?ip=127.0.0.1');
            displayResult('config-result', result);
        }
    </script>
</body>
</html>
