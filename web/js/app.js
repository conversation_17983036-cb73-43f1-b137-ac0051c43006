// 全局变量
let currentEditingItem = null;
let connectedClients = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    showPage('items');
    loadItems();
    loadConnectedClients();
});

// 显示指定页面
function showPage(pageName) {
    document.querySelectorAll('.page-content').forEach(page => {
        page.style.display = 'none';
    });
    
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    document.getElementById(pageName + '-page').style.display = 'block';
    document.querySelector(`[onclick="showPage('${pageName}')"]`).classList.add('active');
    
    switch(pageName) {
        case 'items':
            loadItems();
            break;
        case 'nodes':
            loadNodes();
            break;
        case 'dns':
            loadDnsList();
            break;
    }
}

// API请求封装
async function apiRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('API request failed:', error);
        showAlert('请求失败: ' + error.message, 'danger');
        throw error;
    }
}

// 显示提示消息
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// 显示/隐藏加载模态框
function showLoading() {
    const modal = new bootstrap.Modal(document.getElementById('loadingModal'));
    modal.show();
}

function hideLoading() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('loadingModal'));
    if (modal) modal.hide();
}

// ==================== Item管理功能 ====================

// 加载Items列表
async function loadItems() {
    try {
        const response = await apiRequest('/api/items');
        const items = response.data || [];
        
        const tbody = document.getElementById('items-tbody');
        tbody.innerHTML = '';
        
        if (items.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-muted py-4">
                        <i class="bi bi-inbox fs-1"></i>
                        <div>暂无Item数据</div>
                    </td>
                </tr>
            `;
            return;
        }
        
        items.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.id}</td>
                <td>${item.name}</td>
                <td>${item.target}</td>
                <td><span class="badge bg-primary">${item.method}</span></td>
                <td>
                    <span class="status-badge ${item.status === 1 ? 'status-enabled' : 'status-disabled'}">
                        ${item.status === 1 ? '启用' : '禁用'}
                    </span>
                </td>
                <td>${item.owner || '-'}</td>
                <td>${item.interval || 30}</td>
                <td class="action-buttons">
                    <button class="btn btn-sm btn-outline-primary" onclick="editItem('${item.name}', '${item.target}', '${item.method}', ${item.status}, '${item.owner || ''}', ${item.interval || 30})">
                        <i class="bi bi-pencil"></i> 编辑
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteItem('${item.name}')">
                        <i class="bi bi-trash"></i> 删除
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('加载Items失败:', error);
    }
}

// 显示添加Item模态框
function showAddItemModal() {
    currentEditingItem = null;
    document.getElementById('itemModalTitle').textContent = '添加Item';
    document.getElementById('itemForm').reset();
    document.getElementById('itemDataFields').innerHTML = '';
    
    const modal = new bootstrap.Modal(document.getElementById('itemModal'));
    modal.show();
}

// 编辑Item
function editItem(name, target, method, status, owner, interval) {
    currentEditingItem = { name, target, method, status, owner, interval };
    
    document.getElementById('itemModalTitle').textContent = '编辑Item';
    document.getElementById('itemTarget').value = target;
    document.getElementById('itemMethod').value = method;
    document.getElementById('itemOwner').value = owner;
    document.getElementById('itemInterval').value = interval;
    document.getElementById('itemStatus').value = status;
    
    updateItemDataFields();
    
    const modal = new bootstrap.Modal(document.getElementById('itemModal'));
    modal.show();
}

// 删除Item
async function deleteItem(itemName) {
    if (!confirm('确定要删除这个Item吗？')) return;
    
    try {
        showLoading();
        await apiRequest(`/api/items/${encodeURIComponent(itemName)}`, {
            method: 'DELETE'
        });
        showAlert('Item删除成功', 'success');
        loadItems();
    } catch (error) {
        console.error('删除Item失败:', error);
        showAlert('删除Item失败', 'danger');
    } finally {
        hideLoading();
    }
}

// 更新Item数据字段
function updateItemDataFields() {
    const method = document.getElementById('itemMethod').value;
    const fieldsContainer = document.getElementById('itemDataFields');
    
    fieldsContainer.innerHTML = '';
    
    if (!method) return;
    
    let fieldsHtml = '<h6 class="mt-3 mb-3">配置参数</h6>';
    
    switch (method) {
        case 'HTTP_GET':
        case 'HTTP_POST':
            fieldsHtml += `
                <div class="mb-3">
                    <label for="itemHeaders" class="form-label">请求头 (JSON格式)</label>
                    <textarea class="form-control" id="itemHeaders" rows="3" placeholder='{"Content-Type": "application/json"}'></textarea>
                </div>
            `;
            if (method === 'HTTP_POST') {
                fieldsHtml += `
                    <div class="mb-3">
                        <label for="itemBody" class="form-label">请求体</label>
                        <textarea class="form-control" id="itemBody" rows="3"></textarea>
                    </div>
                `;
            }
            break;
        case 'TCP':
            fieldsHtml += `
                <div class="mb-3">
                    <label for="itemPort" class="form-label">端口</label>
                    <input type="number" class="form-control" id="itemPort" placeholder="80">
                </div>
            `;
            break;
        case 'DNS':
            fieldsHtml += `
                <div class="mb-3">
                    <label for="itemQueryType" class="form-label">查询类型</label>
                    <select class="form-select" id="itemQueryType">
                        <option value="A">A</option>
                        <option value="AAAA">AAAA</option>
                        <option value="CNAME">CNAME</option>
                        <option value="MX">MX</option>
                        <option value="TXT">TXT</option>
                    </select>
                </div>
            `;
            break;
    }
    
    fieldsContainer.innerHTML = fieldsHtml;
}

// 保存Item
async function saveItem() {
    try {
        const target = document.getElementById('itemTarget').value.trim();
        const method = document.getElementById('itemMethod').value;
        const owner = document.getElementById('itemOwner').value.trim();
        const interval = parseInt(document.getElementById('itemInterval').value) || 30;
        const status = parseInt(document.getElementById('itemStatus').value);
        
        if (!target || !method) {
            showAlert('请填写必填字段', 'warning');
            return;
        }
        
        // 构建item_data
        let itemData = {};
        switch (method) {
            case 'HTTP_GET':
                itemData = {
                    url: target,
                    requestMethod: 'GET',
                    headers: {}
                };
                break;
            case 'HTTP_POST':
                itemData = {
                    url: target,
                    requestMethod: 'POST',
                    headers: {},
                    body: document.getElementById('itemBody')?.value || ''
                };
                break;
            case 'TCP':
                const port = document.getElementById('itemPort')?.value;
                itemData = {
                    host: target,
                    port: parseInt(port) || 80
                };
                break;
            case 'ICMP':
                itemData = { host: target };
                break;
            case 'DNS':
                itemData = {
                    query_name: target,
                    query_type: document.getElementById('itemQueryType')?.value || 'A'
                };
                break;
        }
        
        const itemPayload = {
            target: target,
            method: method,
            owner: owner,
            interval: interval,
            status: status,
            item_data: JSON.stringify(itemData)
        };
        
        showLoading();
        
        await apiRequest('/api/items', {
            method: 'POST',
            body: JSON.stringify(itemPayload)
        });
        
        showAlert('Item保存成功', 'success');
        hideLoading();
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('itemModal'));
        modal.hide();
        loadItems();
        
    } catch (error) {
        hideLoading();
        console.error('保存Item失败:', error);
    }
}

// ==================== Node管理功能 ====================

// 加载连接的客户端列表
async function loadConnectedClients() {
    try {
        const response = await apiRequest('/ws/api/clients');
        connectedClients = response.clients || [];
    } catch (error) {
        console.error('加载连接客户端失败:', error);
        connectedClients = [];
    }
}

// 加载Nodes列表
async function loadNodes() {
    try {
        await loadConnectedClients();
        const response = await apiRequest('/api/nodes');
        const allNodes = response.data || [];

        const tbody = document.getElementById('nodes-tbody');
        tbody.innerHTML = '';

        if (allNodes.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-muted py-4">
                        <i class="bi bi-hdd-network fs-1"></i>
                        <div>暂无节点数据</div>
                    </td>
                </tr>
            `;
            return;
        }

        allNodes.forEach(nodeData => {
            // 检查WebSocket连接状态 - 注意字段名是client_ip
            const isConnected = connectedClients.some(client => client.client_ip === nodeData.ip_addr);

            // 获取节点数据库状态 - 1为启用，0为禁用
            const isEnabled = nodeData.status === 1;

            // 显示状态：连接状态 + 启用状态
            let statusText = '';
            let statusClass = '';

            if (isConnected && isEnabled) {
                statusText = '在线';
                statusClass = 'status-online';
            } else if (isConnected && !isEnabled) {
                statusText = '已连接(禁用)';
                statusClass = 'status-disabled';
            } else if (!isConnected && isEnabled) {
                statusText = '离线';
                statusClass = 'status-offline';
            } else {
                statusText = '离线(禁用)';
                statusClass = 'status-disabled';
            }

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${nodeData.ip_addr}</td>
                <td>${nodeData.city}, ${nodeData.province}</td>
                <td>${nodeData.isp}</td>
                <td>${nodeData.dns_addr || '-'}</td>
                <td>
                    <span class="status-badge ${statusClass}">
                        <i class="bi bi-circle-fill"></i> ${statusText}
                    </span>
                </td>
                <td class="action-buttons">
                    <button class="btn btn-sm btn-outline-primary" onclick="generateAndViewConfigs('${nodeData.ip_addr}')" title="查看配置">
                        <i class="bi bi-file-earmark-code"></i>
                    </button>
                    ${isConnected && isEnabled ? `
                        <button class="btn btn-sm btn-outline-success" onclick="pushConfigToNode('${nodeData.ip_addr}')" title="推送配置">
                            <i class="bi bi-cloud-upload"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="reloadBlackboxConfig('${nodeData.ip_addr}')" title="重新加载Blackbox配置">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    ` : ''}
                    <button class="btn btn-sm btn-outline-warning" onclick="showNodeInfo('${nodeData.ip_addr}')" title="查看详情">
                        <i class="bi bi-info-circle"></i>
                    </button>
                    ${isEnabled ? `
                        <button class="btn btn-sm btn-outline-danger" onclick="offlineNode('${nodeData.ip_addr}')" title="下线节点">
                            <i class="bi bi-power"></i>
                        </button>
                    ` : `
                        <button class="btn btn-sm btn-outline-success" onclick="enableNode('${nodeData.ip_addr}')" title="启用节点">
                            <i class="bi bi-play-circle"></i>
                        </button>
                    `}
                </td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('加载Nodes失败:', error);
    }
}

// 推送配置到指定节点
async function pushConfigToNode(nodeIP) {
    try {
        showLoading();

        const response = await apiRequest('/ws/api/push/node', {
            method: 'POST',
            body: JSON.stringify({
                target_ip: nodeIP,
                message: `推送配置到节点 ${nodeIP}`
            })
        });

        hideLoading();
        showAlert(`配置推送成功到节点 ${nodeIP}`, 'success');
    } catch (error) {
        hideLoading();
        console.error('推送配置失败:', error);
    }
}

// 推送所有配置
async function pushAllConfigs() {
    if (!confirm('确定要推送配置到所有节点吗？')) return;

    try {
        showLoading();

        const response = await apiRequest('/ws/api/push/configs', {
            method: 'POST'
        });

        hideLoading();
        showAlert('配置推送成功到所有节点', 'success');
    } catch (error) {
        hideLoading();
        console.error('推送所有配置失败:', error);
    }
}

// 重新加载Blackbox配置
async function reloadBlackboxConfig(nodeIP) {
    try {
        showLoading();

        const response = await apiRequest('/ws/api/reload/blackbox', {
            method: 'POST',
            body: JSON.stringify({
                target_ip: nodeIP
            })
        });

        hideLoading();
        showAlert(`节点 ${nodeIP} 的Blackbox配置重载命令已发送`, 'success');
    } catch (error) {
        hideLoading();
        console.error('重新加载Blackbox配置失败:', error);
    }
}

// 生成并查看配置
async function generateAndViewConfigs(nodeIP) {
    try {
        showLoading();

        const response = await apiRequest(`/api/configs/generate/all?ip=${nodeIP}`);

        hideLoading();

        if (response.data) {
            showConfigModal(nodeIP, response.data);
        }
    } catch (error) {
        hideLoading();
        console.error('生成配置失败:', error);
    }
}

// 显示配置模态框
function showConfigModal(nodeIP, configData) {
    const modalHtml = `
        <div class="modal fade" id="configModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">节点配置 - ${nodeIP}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <ul class="nav nav-tabs" id="configTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="blackbox-tab" data-bs-toggle="tab" data-bs-target="#blackbox-pane" type="button" role="tab">
                                    Blackbox配置 (${configData.blackbox.modules} 模块)
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="vmagent-tab" data-bs-toggle="tab" data-bs-target="#vmagent-pane" type="button" role="tab">
                                    VMAgent配置
                                </button>
                            </li>
                        </ul>
                        <div class="tab-content" id="configTabContent">
                            <div class="tab-pane fade show active" id="blackbox-pane" role="tabpanel">
                                <div class="mt-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6>Blackbox Exporter 配置</h6>
                                        <button class="btn btn-sm btn-outline-primary" onclick="copyToClipboard('blackbox-config')">
                                            <i class="bi bi-clipboard"></i> 复制
                                        </button>
                                    </div>
                                    <pre class="code-block" id="blackbox-config">${configData.blackbox.config_content}</pre>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="vmagent-pane" role="tabpanel">
                                <div class="mt-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6>VMAgent 配置</h6>
                                        <button class="btn btn-sm btn-outline-primary" onclick="copyToClipboard('vmagent-config')">
                                            <i class="bi bi-clipboard"></i> 复制
                                        </button>
                                    </div>
                                    <pre class="code-block" id="vmagent-config">${configData.vmagent.config_content}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-success" onclick="pushConfigsToNode('${nodeIP}')">
                            <i class="bi bi-cloud-upload"></i> 推送配置到节点
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('configModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    const modal = new bootstrap.Modal(document.getElementById('configModal'));
    modal.show();

    // 模态框关闭后移除DOM
    document.getElementById('configModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// 复制到剪贴板
async function copyToClipboard(elementId) {
    try {
        const element = document.getElementById(elementId);
        const text = element.textContent;

        await navigator.clipboard.writeText(text);
        showAlert('配置已复制到剪贴板', 'success');
    } catch (error) {
        console.error('复制失败:', error);
        showAlert('复制失败', 'danger');
    }
}

// 推送配置到节点
async function pushConfigsToNode(nodeIP) {
    try {
        showLoading();

        // 这里可以调用推送配置的API
        const response = await apiRequest('/ws/api/push/node', {
            method: 'POST',
            body: JSON.stringify({
                target_ip: nodeIP,
                message: `推送新配置到节点 ${nodeIP}`
            })
        });

        hideLoading();
        showAlert(`配置推送成功到节点 ${nodeIP}`, 'success');

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('configModal'));
        if (modal) {
            modal.hide();
        }
    } catch (error) {
        hideLoading();
        console.error('推送配置失败:', error);
    }
}

// 显示节点详细信息
async function showNodeInfo(nodeIP) {
    try {
        showLoading();

        const nodeResponse = await apiRequest(`/api/node?ip=${nodeIP}`);
        const nodeData = nodeResponse.data;

        hideLoading();

        // 创建信息模态框
        const modalHtml = `
            <div class="modal fade" id="nodeInfoModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">节点信息 - ${nodeIP}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>基本信息</h6>
                                    <table class="table table-sm">
                                        <tr><td>IP地址:</td><td>${nodeData.ip_addr}</td></tr>
                                        <tr><td>大洲:</td><td>${nodeData.continent}</td></tr>
                                        <tr><td>城市:</td><td>${nodeData.city}</td></tr>
                                        <tr><td>省份:</td><td>${nodeData.province}</td></tr>
                                        <tr><td>ISP:</td><td>${nodeData.isp}</td></tr>
                                        <tr><td>ISP代码:</td><td>${nodeData.isp_code}</td></tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6>网络信息</h6>
                                    <table class="table table-sm">
                                        <tr><td>DNS服务器:</td><td>${nodeData.dns_addr || '-'}</td></tr>
                                        <tr><td>IP协议:</td><td>${nodeData.ip_protocol}</td></tr>
                                        <tr><td>邮编:</td><td>${nodeData.zip_code || '-'}</td></tr>
                                        <tr><td>经度:</td><td>${nodeData.lngwgs || '-'}</td></tr>
                                        <tr><td>纬度:</td><td>${nodeData.latwgs || '-'}</td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('nodeInfoModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        const modal = new bootstrap.Modal(document.getElementById('nodeInfoModal'));
        modal.show();

        // 模态框关闭后移除DOM
        document.getElementById('nodeInfoModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });

    } catch (error) {
        hideLoading();
        console.error('获取节点信息失败:', error);
    }
}

// 下线节点
async function offlineNode(nodeIP) {
    if (!confirm(`确定要下线节点 ${nodeIP} 吗？`)) return;

    try {
        showLoading();

        await apiRequest('/api/nodes/status', {
            method: 'PUT',
            body: JSON.stringify({
                ip_addr: nodeIP,
                status: 0
            })
        });

        hideLoading();
        showAlert(`节点 ${nodeIP} 已下线`, 'success');
        loadNodes();
    } catch (error) {
        hideLoading();
        console.error('下线节点失败:', error);
    }
}

// 启用节点
async function enableNode(nodeIP) {
    if (!confirm(`确定要启用节点 ${nodeIP} 吗？`)) return;

    try {
        showLoading();

        await apiRequest('/api/nodes/status', {
            method: 'PUT',
            body: JSON.stringify({
                ip_addr: nodeIP,
                status: 1
            })
        });

        hideLoading();
        showAlert(`节点 ${nodeIP} 已启用`, 'success');
        loadNodes();
    } catch (error) {
        hideLoading();
        console.error('启用节点失败:', error);
    }
}



// 刷新节点列表
function refreshNodes() {
    loadNodes();
    showAlert('节点列表已刷新', 'info');
}



// ==================== DNS管理功能 ====================

// 加载DNS列表
async function loadDnsList() {
    try {
        const response = await apiRequest('/api/dns');
        const dnsList = response.data || [];

        const tbody = document.getElementById('dns-tbody');
        tbody.innerHTML = '';

        if (dnsList.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-muted py-4">
                        <i class="bi bi-globe fs-1"></i>
                        <div>暂无DNS数据</div>
                    </td>
                </tr>
            `;
            return;
        }

        dnsList.forEach(dns => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${dns.id}</td>
                <td>${dns.ip_addr}</td>
                <td>${dns.city}, ${dns.province}</td>
                <td>${dns.isp}</td>
                <td>
                    <span class="status-badge ${dns.status === 1 ? 'status-enabled' : 'status-disabled'}">
                        ${dns.status === 1 ? '启用' : '禁用'}
                    </span>
                </td>
                <td class="action-buttons">
                    <button class="btn btn-sm btn-outline-warning" onclick="showDnsInfo(${dns.id})" title="查看详情">
                        <i class="bi bi-info-circle"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteDns(${dns.id})" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('加载DNS列表失败:', error);
    }
}

// 显示添加DNS模态框
function showAddDnsModal() {
    document.getElementById('dnsForm').reset();

    const modal = new bootstrap.Modal(document.getElementById('dnsModal'));
    modal.show();
}

// 保存DNS
async function saveDns() {
    try {
        const ipAddr = document.getElementById('dnsIpAddr').value.trim();
        const owner = document.getElementById('dnsOwner').value.trim();

        if (!ipAddr) {
            showAlert('请填写IP地址', 'warning');
            return;
        }

        // 验证IP地址格式
        const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        if (!ipRegex.test(ipAddr)) {
            showAlert('请输入有效的IP地址', 'warning');
            return;
        }

        const dnsPayload = {
            ip_addr: ipAddr,
            owner: owner
        };

        showLoading();

        await apiRequest('/api/dns', {
            method: 'POST',
            body: JSON.stringify(dnsPayload)
        });

        showAlert('DNS记录创建成功', 'success');
        hideLoading();

        const modal = bootstrap.Modal.getInstance(document.getElementById('dnsModal'));
        modal.hide();
        loadDnsList();

    } catch (error) {
        hideLoading();
        console.error('保存DNS失败:', error);
    }
}

// 显示DNS详细信息
function showDnsInfo(dnsId) {
    // 从表格中获取DNS数据
    const rows = document.querySelectorAll('#dns-tbody tr');
    let row = null;
    for (let r of rows) {
        if (r.cells[0].textContent === dnsId.toString()) {
            row = r;
            break;
        }
    }
    if (!row) return;

    const cells = row.querySelectorAll('td');
    const dnsData = {
        id: cells[0].textContent,
        ip_addr: cells[1].textContent,
        location: cells[2].textContent,
        isp: cells[3].textContent,
        status: cells[4].textContent.includes('启用') ? '启用' : '禁用'
    };

    const modalHtml = `
        <div class="modal fade" id="dnsInfoModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">DNS信息 - ${dnsData.ip_addr}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <table class="table table-sm">
                            <tr><td>ID:</td><td>${dnsData.id}</td></tr>
                            <tr><td>IP地址:</td><td>${dnsData.ip_addr}</td></tr>
                            <tr><td>位置:</td><td>${dnsData.location}</td></tr>
                            <tr><td>ISP:</td><td>${dnsData.isp}</td></tr>
                            <tr><td>状态:</td><td>${dnsData.status}</td></tr>
                        </table>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('dnsInfoModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    const modal = new bootstrap.Modal(document.getElementById('dnsInfoModal'));
    modal.show();

    // 模态框关闭后移除DOM
    document.getElementById('dnsInfoModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// 删除DNS
async function deleteDns(dnsId) {
    if (!confirm('确定要删除这个DNS记录吗？')) return;

    showAlert('删除功能需要后端支持DELETE接口', 'warning');
}

// 重新加载DNS列表
async function reloadDnsList() {
    if (!confirm('确定要重新加载DNS列表吗？这将从dns_list文件中重新导入数据。')) return;

    try {
        showLoading();

        await apiRequest('/api/dns/reload');

        hideLoading();
        showAlert('DNS列表重新加载成功', 'success');
        loadDnsList();
    } catch (error) {
        hideLoading();
        console.error('重新加载DNS列表失败:', error);
    }
}

// ==================== Node管理功能 ====================

// 加载Nodes列表
async function loadNodes() {
    try {
        const response = await apiRequest('/api/nodes');
        const nodes = response.data || [];
        
        const tbody = document.getElementById('nodes-tbody');
        tbody.innerHTML = '';
        
        if (nodes.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center text-muted py-4">
                        <i class="bi bi-hdd fs-1"></i>
                        <div>暂无Node数据</div>
                    </td>
                </tr>
            `;
            return;
        }
        
        nodes.forEach(node => {
            const row = document.createElement('tr');
            const isOnline = connectedClients.some(client => client.ip === node.ip_addr);
            const statusBadge = isOnline ? 
                '<span class="badge bg-success">在线</span>' : 
                '<span class="badge bg-secondary">离线</span>';
            
            row.innerHTML = `
                <td>${node.ip_addr}</td>
                <td>${node.location || '-'}</td>
                <td>${node.isp || '-'}</td>
                <td>${node.dns || '-'}</td>
                <td>${statusBadge}</td>
                <td>${node.updated_at || '-'}</td>
                <td class="action-buttons">
                    <button class="btn btn-sm btn-outline-info" onclick="showNodeDetail('${node.ip_addr}')">
                        <i class="bi bi-info-circle"></i> 详情
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="pushNodeConfig('${node.ip_addr}')">
                        <i class="bi bi-cloud-upload"></i> 推送
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="reloadNodeBlackbox('${node.ip_addr}')">
                        <i class="bi bi-arrow-repeat"></i> 重载
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="offlineNode('${node.ip_addr}')">
                        <i class="bi bi-power"></i> 下线
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('加载Nodes失败:', error);
    }
}

// 显示Node详情
async function showNodeDetail(nodeIp) {
    try {
        showLoading();
        const response = await apiRequest(`/api/node?ip=${nodeIp}`);
        const node = response.data;
        
        const content = `
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <table class="table table-sm">
                        <tr><td>IP地址:</td><td>${node.ip_addr}</td></tr>
                        <tr><td>位置:</td><td>${node.location || '-'}</td></tr>
                        <tr><td>ISP:</td><td>${node.isp || '-'}</td></tr>
                        <tr><td>DNS:</td><td>${node.dns || '-'}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>状态信息</h6>
                    <table class="table table-sm">
                        <tr><td>创建时间:</td><td>${node.created_at || '-'}</td></tr>
                        <tr><td>更新时间:</td><td>${node.updated_at || '-'}</td></tr>
                        <tr><td>连接状态:</td><td>${connectedClients.some(c => c.ip === nodeIp) ? '在线' : '离线'}</td></tr>
                    </table>
                </div>
            </div>
        `;
        
        document.getElementById('nodeDetailContent').innerHTML = content;
        const modal = new bootstrap.Modal(document.getElementById('nodeDetailModal'));
        modal.show();
    } catch (error) {
        console.error('获取Node详情失败:', error);
    } finally {
        hideLoading();
    }
}

// 推送Node配置
async function pushNodeConfig(nodeIp) {
    try {
        showLoading();
        await apiRequest('/ws/api/push/configs', {
            method: 'POST',
            body: JSON.stringify({ target_ip: nodeIp })
        });
        showAlert(`配置推送到 ${nodeIp} 成功`, 'success');
    } catch (error) {
        console.error('推送配置失败:', error);
        showAlert('推送配置失败', 'danger');
    } finally {
        hideLoading();
    }
}

// 推送所有配置
async function pushAllConfigs() {
    if (!confirm('确定要推送配置到所有在线节点吗？')) return;
    
    try {
        showLoading();
        await apiRequest('/ws/api/push/configs', {
            method: 'POST',
            body: JSON.stringify({})
        });
        showAlert('配置推送成功', 'success');
    } catch (error) {
        console.error('推送所有配置失败:', error);
        showAlert('推送配置失败', 'danger');
    } finally {
        hideLoading();
    }
}

// 重载Node Blackbox配置
async function reloadNodeBlackbox(nodeIp) {
    try {
        showLoading();
        await apiRequest(`/api/blackbox/reload?ip=${nodeIp}`, {
            method: 'POST'
        });
        showAlert(`${nodeIp} Blackbox配置重载成功`, 'success');
    } catch (error) {
        console.error('重载Blackbox配置失败:', error);
        showAlert('重载配置失败', 'danger');
    } finally {
        hideLoading();
    }
}

// 重载所有Blackbox配置
async function reloadAllBlackbox() {
    if (!confirm('确定要重载所有在线节点的Blackbox配置吗？')) return;
    
    try {
        showLoading();
        await apiRequest('/api/blackbox/reload-all', {
            method: 'POST'
        });
        showAlert('所有节点Blackbox配置重载成功', 'success');
    } catch (error) {
        console.error('重载所有Blackbox配置失败:', error);
        showAlert('重载配置失败', 'danger');
    } finally {
        hideLoading();
    }
}

// 下线Node
async function offlineNode(nodeIp) {
    if (!confirm(`确定要下线节点 ${nodeIp} 吗？`)) return;
    
    try {
        showLoading();
        await apiRequest(`/api/nodes/${nodeIp}/offline`, {
            method: 'POST'
        });
        showAlert(`节点 ${nodeIp} 已下线`, 'success');
        loadNodes();
    } catch (error) {
        console.error('下线节点失败:', error);
        showAlert('下线节点失败', 'danger');
    } finally {
        hideLoading();
    }
}

// 刷新Nodes
function refreshNodes() {
    loadNodes();
    loadConnectedClients();
}

// ==================== DNS管理功能 ====================

// 加载DNS列表
async function loadDnsList() {
    try {
        const response = await apiRequest('/api/dns');
        const dnsList = response.data || [];
        
        const tbody = document.getElementById('dns-tbody');
        tbody.innerHTML = '';
        
        if (dnsList.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center text-muted py-4">
                        <i class="bi bi-globe fs-1"></i>
                        <div>暂无DNS数据</div>
                    </td>
                </tr>
            `;
            return;
        }
        
        dnsList.forEach(dns => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${dns.id}</td>
                <td>${dns.ip_addr}</td>
                <td>${dns.location || '-'}</td>
                <td>${dns.isp || '-'}</td>
                <td>${dns.owner || '-'}</td>
                <td>
                    <span class="badge ${dns.status === 1 ? 'bg-success' : 'bg-secondary'}">
                        ${dns.status === 1 ? '启用' : '禁用'}
                    </span>
                </td>
                <td class="action-buttons">
                    <button class="btn btn-sm btn-outline-primary" onclick="editDns(${dns.id}, '${dns.ip_addr}', '${dns.owner || ''}', ${dns.status})">
                        <i class="bi bi-pencil"></i> 编辑
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteDns(${dns.id})">
                        <i class="bi bi-trash"></i> 删除
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('加载DNS列表失败:', error);
    }
}

// 显示添加DNS模态框
function showAddDnsModal() {
    document.getElementById('dnsForm').reset();
    const modal = new bootstrap.Modal(document.getElementById('dnsModal'));
    modal.show();
}

// 编辑DNS
function editDns(id, ipAddr, owner, status) {
    document.getElementById('dnsIpAddr').value = ipAddr;
    document.getElementById('dnsOwner').value = owner;
    document.getElementById('dnsIpAddr').dataset.editId = id;
    
    const modal = new bootstrap.Modal(document.getElementById('dnsModal'));
    modal.show();
}

// 保存DNS
async function saveDns() {
    try {
        const ipAddr = document.getElementById('dnsIpAddr').value.trim();
        const owner = document.getElementById('dnsOwner').value.trim();
        const editId = document.getElementById('dnsIpAddr').dataset.editId;
        
        if (!ipAddr) {
            showAlert('请填写IP地址', 'warning');
            return;
        }
        
        const dnsPayload = {
            ip_addr: ipAddr,
            owner: owner
        };
        
        showLoading();
        
        if (editId) {
            await apiRequest(`/api/dns/${editId}`, {
                method: 'PUT',
                body: JSON.stringify(dnsPayload)
            });
            showAlert('DNS更新成功', 'success');
        } else {
            await apiRequest('/api/dns', {
                method: 'POST',
                body: JSON.stringify(dnsPayload)
            });
            showAlert('DNS添加成功', 'success');
        }
        
        hideLoading();
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('dnsModal'));
        modal.hide();
        loadDnsList();
        
    } catch (error) {
        hideLoading();
        console.error('保存DNS失败:', error);
    }
}

// 删除DNS
async function deleteDns(dnsId) {
    if (!confirm('确定要删除这个DNS吗？')) return;
    
    try {
        showLoading();
        await apiRequest(`/api/dns/${dnsId}`, {
            method: 'DELETE'
        });
        showAlert('DNS删除成功', 'success');
        loadDnsList();
    } catch (error) {
        console.error('删除DNS失败:', error);
        showAlert('删除DNS失败', 'danger');
    } finally {
        hideLoading();
    }
}

// 重新加载DNS列表
async function reloadDnsList() {
    try {
        showLoading();
        await apiRequest('/api/dns/reload', {
            method: 'POST'
        });
        showAlert('DNS列表重新加载成功', 'success');
        loadDnsList();
    } catch (error) {
        console.error('重新加载DNS列表失败:', error);
        showAlert('重新加载失败', 'danger');
    } finally {
        hideLoading();
    }
}

// 加载连接的客户端
async function loadConnectedClients() {
    try {
        const response = await apiRequest('/ws/api/clients');
        connectedClients = response.data || [];
    } catch (error) {
        console.error('加载连接客户端失败:', error);
        connectedClients = [];
    }
}