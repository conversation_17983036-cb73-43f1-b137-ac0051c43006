# Boce Service 前端管理页面

## 功能概述

本前端管理页面提供了完整的 Boce Service 管理功能，包括：

### 1. Item 管理（增删改查）
- **查看 Item 列表**：显示所有监控项目，包括 ID、名称、目标、方法、状态等信息
- **添加 Item**：支持创建 HTTP_GET、HTTP_POST、TCP、ICMP、DNS 等类型的监控项目
- **编辑 Item**：修改现有监控项目的配置
- **删除 Item**：软删除监控项目（设置状态为禁用）
- **动态配置**：根据不同的监控方法显示相应的配置字段

### 2. Node 管理
- **节点列表**：显示所有注册的节点及其状态（在线/离线）
- **推送配置**：向指定节点或所有节点推送最新配置文件
- **重新加载 Blackbox 配置**：为指定节点重新生成 Blackbox 配置
- **节点详情**：查看节点的详细信息（位置、ISP、DNS 等）
- **下线节点**：将节点状态设置为离线
- **实时状态**：显示节点的在线/离线状态

### 3. DNS 管理
- **DNS 列表**：显示所有 DNS 记录
- **添加 DNS**：手动添加新的 DNS 记录
- **DNS 详情**：查看 DNS 记录的详细信息
- **重新加载 DNS 列表**：从 dns_list 文件重新导入 DNS 数据

## 技术实现

### 前端技术栈
- **HTML5 + CSS3**：页面结构和样式
- **Bootstrap 5**：响应式 UI 框架
- **JavaScript (ES6+)**：交互逻辑和 API 调用
- **Bootstrap Icons**：图标库

### 后端 API 接口

#### Item 管理 API
- `GET /api/items` - 获取 Item 列表
- `POST /api/items` - 创建新 Item
- `PUT /api/items` - 更新 Item
- `DELETE /api/items/:name` - 删除 Item
- `POST /api/items/batch` - 批量创建/更新 Item

#### Node 管理 API
- `GET /api/nodes` - 获取所有节点列表
- `GET /api/node?ip=<ip>` - 获取指定节点信息
- `PUT /api/nodes/status` - 更新节点状态
- `GET /ws/api/clients` - 获取连接的客户端列表
- `POST /ws/api/files/push` - 推送配置到指定节点
- `POST /ws/api/push/configs` - 推送配置到所有节点

#### DNS 管理 API
- `GET /api/dns` - 获取 DNS 列表
- `POST /api/dns` - 创建 DNS 记录
- `GET /api/dns/reload` - 重新加载 DNS 列表
- `GET /api/ip/aw_meta?ip=<ip>` - 获取 IP 元数据

#### Blackbox 配置 API
- `GET /api/blackbox/generate/base64?ip=<ip>` - 生成节点的 Blackbox 配置
- `GET /api/blackbox/generate/default` - 生成默认 Blackbox 配置

## 使用说明

### 启动服务
1. 确保后端服务正在运行
2. 访问 `http://localhost:8080/web/` 打开管理页面

### Item 管理操作
1. **添加 Item**：
   - 点击"添加 Item"按钮
   - 填写目标地址和选择监控方法
   - 根据方法类型配置相应参数
   - 点击保存

2. **编辑 Item**：
   - 在 Item 列表中点击"编辑"按钮
   - 修改相应字段
   - 点击保存

3. **删除 Item**：
   - 在 Item 列表中点击"删除"按钮
   - 确认删除操作

### Node 管理操作
1. **查看节点状态**：
   - 切换到"Node 管理"页面
   - 查看所有节点的在线/离线状态

2. **推送配置**：
   - 点击节点行的推送按钮推送到单个节点
   - 点击"推送所有配置"按钮推送到所有节点

3. **查看节点详情**：
   - 点击节点行的信息按钮查看详细信息

4. **下线节点**：
   - 点击节点行的下线按钮将节点设置为离线状态

### DNS 管理操作
1. **添加 DNS**：
   - 点击"添加 DNS"按钮
   - 输入 IP 地址和所有者信息
   - 系统会自动查询 IP 的地理位置信息

2. **重新加载 DNS 列表**：
   - 点击"重新加载 DNS 列表"按钮
   - 系统会从 dns_list 文件重新导入数据

## 特性说明

### 响应式设计
- 支持桌面和移动设备访问
- 自适应不同屏幕尺寸

### 实时状态更新
- 节点在线/离线状态实时显示
- 操作结果即时反馈

### 用户友好界面
- 直观的操作界面
- 详细的状态提示
- 确认对话框防止误操作

### 错误处理
- 完善的错误提示机制
- 网络请求失败自动重试
- 表单验证和数据校验

## 文件结构
```
web/
├── index.html          # 主页面
├── css/
│   └── style.css      # 样式文件
├── js/
│   └── app.js         # 主要 JavaScript 逻辑
└── README.md          # 说明文档
```

## 浏览器兼容性
- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## 注意事项
1. 确保后端服务正常运行
2. 检查网络连接状态
3. 某些操作需要管理员权限
4. 建议使用现代浏览器以获得最佳体验
