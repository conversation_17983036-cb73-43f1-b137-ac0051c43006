package model

import (
	"gorm.io/gorm"
)

var DB *gorm.DB

// GetDB 获取数据库连接
func GetDB() *gorm.DB {
	return DB
}

// SetDB 设置数据库连接
func SetDB(database *gorm.DB) {
	DB = database
}

func (t DNS) TableName() string {
	return "dns"
}

const (
	DNS_ON_STATUS  = 1
	DNS_OFF_STATUS = 0
)

var ISP_MAP = map[string]string{
	"中国移动": "MOB",
	"中国联通": "CNC",
	"中国电信": "CTL",
	"BGP":  "BGP",
	"阿里巴巴": "ALI",
}

type DNS struct {
	gorm.Model
	Id        int    `gorm:"primaryKey" json:"id"`
	Accuracy  string `gorm:"column:accuracy" json:"accuracy"`
	IpAddr    string `gorm:"column:ip_addr" json:"ip_addr"`
	Continent string `gorm:"column:continent" json:"continent"`
	Country   string `gorm:"column:country" json:"country"`
	Province  string `gorm:"column:province" json:"province"`
	City      string `gorm:"column:city" json:"city"`
	District  string `gorm:"column:district" json:"district"`
	AreaCode  string `gorm:"column:area_code" json:"area_code"`
	AdCode    string `gorm:"column:ad_code" json:"ad_code"`
	Isp       string `gorm:"column:isp" json:"isp"`
	IspCode   string `gorm:"column:isp_code" json:"isp_code"`
	ZipCode   string `gorm:"column:zip_code" json:"zip_code"`
	AsNumber  string `gorm:"column:as_number" json:"as_number"`
	Lngwgs    string `gorm:"column:lngwgs" json:"lngwgs"`
	Latwgs    string `gorm:"column:latwgs" json:"latwgs"`
	Status    int    `gorm:"column:status" json:"status"`
	Owner     string `gorm:"column:owner" json:"owner"`
}

func BatchCreateOrUpdateDNS(records []DNS) error {
	if len(records) == 0 {
		return nil
	}
	db := GetDB()
	// 分批处理，每批处理200条记录
	batchSize := 200
	for i := 0; i < len(records); i += batchSize {
		end := i + batchSize
		if end > len(records) {
			end = len(records)
		}

		batch := records[i:end]

		// 开始事务
		tx := db.Begin()
		if tx.Error != nil {
			return tx.Error
		}

		for _, record := range batch {
			var existingRecord DNS
			err := tx.Where("ip_addr = ?", record.IpAddr).First(&existingRecord).Error

			if err == gorm.ErrRecordNotFound {
				// 创建新记录
				if err := tx.Create(&record).Error; err != nil {
					tx.Rollback()
					return err
				}
			} else if err != nil {
				tx.Rollback()
				return err
			} else {
				// 更新现有记录
				if err := tx.Model(&existingRecord).Updates(record).Error; err != nil {
					tx.Rollback()
					return err
				}
			}
		}
		// 提交事务
		if err := tx.Commit().Error; err != nil {
			return err
		}
	}
	return nil
}

func GetDNSbyFilter(filter string) []DNS {
	var dns []DNS
	db := GetDB()
	db.Where(filter).Find(&dns)
	return dns
}

// GetDNSById 根据ID查找DNS记录
func GetDNSById(id int) DNS {
	var dns DNS
	db := GetDB()
	db.Where("id = ? AND status = ?",
		id, DNS_ON_STATUS).First(&dns)
	return dns
}

// GetDNSByCityAndISP 根据城市和ISP查找DNS记录
func GetDNSByCityAndISP(city, province, isp string) []DNS {
	var dns []DNS
	db := GetDB()
	db.Where("city = ? AND province = ? AND isp = ? AND status = ?",
		city, province, isp, DNS_ON_STATUS).Find(&dns)
	return dns
}

// GetDNSByProvinceAndISP 根据省份和ISP查找DNS记录
func GetDNSByProvinceAndISP(province, isp string) []DNS {
	var dns []DNS
	db := GetDB()
	db.Where("province = ? AND isp = ? AND status = ?",
		province, isp, DNS_ON_STATUS).Find(&dns)
	return dns
}
