package model

import (
	"time"
)

// BlackboxConfig 表示blackbox_exporter的完整配置
type BlackboxConfig struct {
	Modules []BlackboxModule `yaml:"modules" json:"modules"`
}

// BlackboxModule 表示一个blackbox模块配置
type BlackboxModule struct {
	Name    string        `yaml:"-" json:"name"`
	Prober  string        `yaml:"prober" json:"prober"`
	Timeout time.Duration `yaml:"timeout" json:"timeout"`
	HTTP    *HTTPProbe    `yaml:"http,omitempty" json:"http,omitempty"`
	TCP     *TCPProbe     `yaml:"tcp,omitempty" json:"tcp,omitempty"`
	ICMP    *ICMPProbe    `yaml:"icmp,omitempty" json:"icmp,omitempty"`
	DNS     *DNSProbe     `yaml:"dns,omitempty" json:"dns,omitempty"`
}

// HTTPProbe HTTP探测配置
type HTTPProbe struct {
	ValidHTTPVersions []string          `yaml:"valid_http_versions,omitempty" json:"valid_http_versions,omitempty"`
	ValidStatusCodes  []int             `yaml:"valid_status_codes,omitempty" json:"valid_status_codes,omitempty"`
	Method            string            `yaml:"method,omitempty" json:"method,omitempty"`
	Headers           map[string]string `yaml:"headers,omitempty" json:"headers,omitempty"`
	Body              string            `yaml:"body,omitempty" json:"body,omitempty"`
	NoFollowRedirects bool              `yaml:"no_follow_redirects,omitempty" json:"no_follow_redirects,omitempty"`
	FailIfSSL         bool              `yaml:"fail_if_ssl,omitempty" json:"fail_if_ssl,omitempty"`
	FailIfNotSSL      bool              `yaml:"fail_if_not_ssl,omitempty" json:"fail_if_not_ssl,omitempty"`
	TLSConfig         *TLSConfig        `yaml:"tls_config,omitempty" json:"tls_config,omitempty"`
	DnsServer         string            `yaml:"dns_server,omitempty" json:"dns_server,omitempty"`
}

// TCPProbe TCP探测配置
type TCPProbe struct {
	QueryResponse       []QueryResponse `yaml:"query_response,omitempty" json:"query_response,omitempty"`
	TLSConfig           *TLSConfig      `yaml:"tls_config,omitempty" json:"tls_config,omitempty"`
	PreferredIPProtocol string          `yaml:"preferred_ip_protocol,omitempty" json:"preferred_ip_protocol,omitempty"`
}

// ICMPProbe ICMP探测配置
type ICMPProbe struct {
	PreferredIPProtocol string `yaml:"preferred_ip_protocol,omitempty" json:"preferred_ip_protocol,omitempty"`
}

// DNSProbe DNS探测配置
type DNSProbe struct {
	QueryName      string          `yaml:"query_name,omitempty" json:"query_name,omitempty"`
	QueryType      string          `yaml:"query_type,omitempty" json:"query_type,omitempty"`
	ValidRcodes    []string        `yaml:"valid_rcodes,omitempty" json:"valid_rcodes,omitempty"`
	ValidateAnswer *ValidateAnswer `yaml:"validate_answer_rrs,omitempty" json:"validate_answer_rrs,omitempty"`
}

// QueryResponse TCP查询响应配置
type QueryResponse struct {
	Expect string `yaml:"expect,omitempty" json:"expect,omitempty"`
	Send   string `yaml:"send,omitempty" json:"send,omitempty"`
}

// TLSConfig TLS配置
type TLSConfig struct {
	InsecureSkipVerify bool   `yaml:"insecure_skip_verify,omitempty" json:"insecure_skip_verify,omitempty"`
	ServerName         string `yaml:"server_name,omitempty" json:"server_name,omitempty"`
}

// ValidateAnswer DNS答案验证配置
type ValidateAnswer struct {
	FailIfMatchesRegexp    []string `yaml:"fail_if_matches_regexp,omitempty" json:"fail_if_matches_regexp,omitempty"`
	FailIfNotMatchesRegexp []string `yaml:"fail_if_not_matches_regexp,omitempty" json:"fail_if_not_matches_regexp,omitempty"`
}
