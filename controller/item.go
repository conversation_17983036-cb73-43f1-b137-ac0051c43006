package controller

import (
	"boce-service/model"
	"boce-service/utils/log"
	"github.com/gin-gonic/gin"
	"net/http"
)

// BatchCreateOrUpdateItems 批量创建Item记录
func BatchCreateOrUpdateItems(c *gin.Context) {
	var items []model.Item

	if err := c.ShouldBindJSON(&items); err != nil {
		log.Error("解析JSON失败:", err)
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数格式错误",
		})
		return
	}

	if len(items) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Item列表不能为空",
		})
		return
	}

	log.Infof("开始批量创建 %d 条Item记录", len(items))

	// 强制DNS类型使用高级配置
	for i := range items {
		if items[i].Method == model.ITEM_DNS_Method {
			items[i].IsAdvanced = model.ITEM_ADVANCED_CONFIG
			log.Infof("DNS类型Item强制设置为高级配置: %s", items[i].Name)
		}
	}

	// 使用批量插入方法
	if err := model.BatchCreateOrUpdateItems(items); err != nil {
		log.Error("批量创建Item失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "批量创建失败",
		})
		return
	}

	log.Infof("成功批量创建 %d 条Item记录", len(items))
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "批量创建成功",
		"count":   len(items),
	})
}

// CreateItem 创建单条Item记录
func CreateItem(c *gin.Context) {
	var item model.Item

	if err := c.ShouldBindJSON(&item); err != nil {
		log.Error("解析JSON失败:", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数格式错误",
		})
		return
	}

	item.Name = item.Target + "_" + item.Method

	// 强制DNS类型使用高级配置
	if item.Method == model.ITEM_DNS_Method {
		item.IsAdvanced = model.ITEM_ADVANCED_CONFIG
		log.Infof("DNS类型Item强制设置为高级配置: %s", item.Name)
	}

	if len(model.GetItemsByFilter("name = '"+item.Name+"'")) > 0 {
		log.Error("查询Item已存在，插入失败:")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询Item已存在，插入失败",
		})
		return
	}

	if err := model.CreateOrUpdateItem(item); err != nil {
		log.Error("创建Item失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    item,
	})
}

// GetItems 查询Item列表（HTTP处理）
func GetItems(c *gin.Context) {
	// 获取所有Item，不限制状态
	items := model.GetItemsByFilter("")

	var data []model.ItemData
	for _, item := range items {
		line := model.ItemData{
			Id:         item.Id,
			Name:       item.Name,
			Target:     item.Target,
			Method:     item.Method,
			Owner:      item.Owner,
			ItemData:   item.ItemData,
			Interval:   item.Interval,
			Status:     item.Status,
			IsAdvanced: item.IsAdvanced,
		}
		data = append(data, line)
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "查询成功",
		"data":    data,
		"count":   len(data),
	})
}

// UpdateItem 更新Item记录
func UpdateItem(c *gin.Context) {
	var item model.Item

	if err := c.ShouldBindJSON(&item); err != nil {
		log.Error("解析JSON失败:", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数格式错误",
		})
		return
	}

	item.Name = item.Target + "_" + item.Method

	// 强制DNS类型使用高级配置
	if item.Method == model.ITEM_DNS_Method {
		item.IsAdvanced = model.ITEM_ADVANCED_CONFIG
		log.Infof("DNS类型Item强制设置为高级配置: %s", item.Name)
	}

	if err := model.CreateOrUpdateItem(item); err != nil {
		log.Error("更新Item失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    item,
	})
}

// DeleteItem 删除Item记录
func DeleteItem(c *gin.Context) {
	itemName := c.Param("name")
	if itemName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Item名称不能为空",
		})
		return
	}

	// 查找Item
	items := model.GetItemsByFilter("name = '" + itemName + "'")
	if len(items) == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "Item不存在",
		})
		return
	}

	// 软删除：将状态设置为禁用
	item := items[0]
	item.Status = model.ITEM_OFF_STATUS
	if err := model.CreateOrUpdateItem(item); err != nil {
		log.Error("删除Item失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}
