package controller

import (
	"boce-service/config"
	"boce-service/model"
	"boce-service/service"
	"boce-service/utils/log"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 允许跨域
	},
}

type WSClient struct {
	conn     *websocket.Conn
	clientID string
	clientIP string
	version  string
	send     chan []byte
	hub      *WSHub
	mu       sync.RWMutex // 添加客户端级别的锁
}

type WSHub struct {
	clients    map[*WSClient]bool
	broadcast  chan []byte
	register   chan *WSClient
	unregister chan *WSClient
	mutex      sync.RWMutex
	// 添加按IP索引的客户端映射，便于快速查找
	clientsByIP map[string]*WSClient
}

type FileMessage struct {
	Type      string            `json:"type"`
	Files     []FileData        `json:"files,omitempty"`
	Auth      *AuthData         `json:"auth,omitempty"`
	Metadata  map[string]string `json:"metadata,omitempty"`
	Timestamp int64             `json:"timestamp"`
}

type FileData struct {
	Path    string `json:"path"`
	Content string `json:"content"`
	Hash    string `json:"hash"`
}

type AuthData struct {
	Token    string `json:"token"`
	ClientID string `json:"client_id"`
	Version  string `json:"version"`
}

var wsHub = &WSHub{
	clients:     make(map[*WSClient]bool),
	clientsByIP: make(map[string]*WSClient),
	broadcast:   make(chan []byte, 256), // 增加缓冲区
	register:    make(chan *WSClient, 64),
	unregister:  make(chan *WSClient, 64),
}

// StartWSHub 启动WebSocket Hub
func StartWSHub() {
	go wsHub.run()
}

func init() {
	go wsHub.run()
}

func (h *WSHub) run() {
	for {
		select {
		case client := <-h.register:
			h.mutex.Lock()
			// 如果该IP已有连接，先断开旧连接
			if oldClient, exists := h.clientsByIP[client.clientIP]; exists {
				log.Warnf("客户端IP %s 已存在连接，断开旧连接", client.clientIP)
				delete(h.clients, oldClient)
				close(oldClient.send)
			}

			h.clients[client] = true
			h.clientsByIP[client.clientIP] = client
			h.mutex.Unlock()

			log.Infof("客户端连接: %s, 版本: %s, IP: %s", client.clientID, client.version, client.clientIP)

		case client := <-h.unregister:
			h.mutex.Lock()
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				delete(h.clientsByIP, client.clientIP)
				close(client.send)
				log.Infof("客户端断开: %s (%s)", client.clientID, client.clientIP)
			}
			h.mutex.Unlock()

		case message := <-h.broadcast:
			h.broadcastMessage(message)
		}
	}
}

// 分离广播逻辑
func (h *WSHub) broadcastMessage(message []byte) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	var failedClients []*WSClient
	for client := range h.clients {
		select {
		case client.send <- message:
		default:
			failedClients = append(failedClients, client)
		}
	}

	// 清理失败的客户端
	if len(failedClients) > 0 {
		go h.cleanupFailedClients(failedClients)
	}
}

func (h *WSHub) cleanupFailedClients(clients []*WSClient) {
	for _, client := range clients {
		h.unregister <- client
	}
}

func WSHandler(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Error("WebSocket升级失败:", err)
		return
	}

	// 获取客户端真实IP
	clientIP := getRealClientIP(c)

	client := &WSClient{
		conn:     conn,
		clientIP: clientIP,
		send:     make(chan []byte, 256),
		hub:      wsHub,
	}

	// 启动读写协程
	go client.writePump()
	go client.readPump()
}

func (c *WSClient) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	// 优化读取限制和超时设置
	c.conn.SetReadLimit(1024) // 增加读取限制
	c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Errorf("WebSocket意外关闭: %v", err)
			}
			break
		}

		// 处理消息
		c.handleMessage(message)
	}
}

func (c *WSClient) handleMessage(message []byte) {
	var msg FileMessage
	if err := json.Unmarshal(message, &msg); err != nil {
		log.Warnf("解析消息失败: %v", err)
		return
	}

	switch msg.Type {
	case "auth":
		c.handleAuth(msg.Auth)
	case "heartbeat":
		c.handleHeartbeat()
	default:
		log.Warnf("未知消息类型: %s", msg.Type)
	}
}

func (c *WSClient) handleAuth(auth *AuthData) {
	if auth == nil {
		c.sendAuthResponse(false)
		return
	}

	if c.authenticate(auth) {
		c.mu.Lock()
		c.clientID = auth.ClientID
		c.version = auth.Version
		c.mu.Unlock()

		c.hub.register <- c
		c.sendAuthResponse(true)
	} else {
		c.sendAuthResponse(false)
	}
}

func (c *WSClient) sendAuthResponse(success bool) {
	msgType := "auth_failed"
	if success {
		msgType = "auth_success"
	}

	response := FileMessage{
		Type:      msgType,
		Timestamp: time.Now().Unix(),
	}
	c.sendMessage(response)
}

func (c *WSClient) handleHeartbeat() {
	response := FileMessage{
		Type:      "heartbeat_response",
		Timestamp: time.Now().Unix(),
	}
	c.sendMessage(response)
}

func (c *WSClient) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			// 支持批量写入
			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// 尝试写入更多排队的消息
			n := len(c.send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

func (c *WSClient) authenticate(auth *AuthData) bool {
	// 简单的token验证，实际项目中应该使用更安全的方式
	expectedToken := generateToken(auth.ClientID)
	return auth.Token == expectedToken
}

func (c *WSClient) sendMessage(msg FileMessage) error {
	data, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("序列化消息失败: %v", err)
	}

	select {
	case c.send <- data:
		return nil
	case <-time.After(5 * time.Second): // 添加超时
		return fmt.Errorf("发送消息超时")
	}
}

func generateToken(clientID string) string {
	hash := md5.Sum([]byte(clientID + "secret_key"))
	return hex.EncodeToString(hash[:])
}

// PushFiles 批量推送文件到所有客户端
func PushFiles(c *gin.Context) {
	var req struct {
		Files []struct {
			Path    string `json:"path" binding:"required"`
			Content string `json:"content" binding:"required"`
		} `json:"files" binding:"required"`
		Metadata map[string]string `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	var files []FileData
	for _, f := range req.Files {
		hash := md5.Sum([]byte(f.Content))
		files = append(files, FileData{
			Path:    f.Path,
			Content: f.Content,
			Hash:    hex.EncodeToString(hash[:]),
		})
	}

	message := FileMessage{
		Type:      "file_push",
		Files:     files,
		Metadata:  req.Metadata,
		Timestamp: time.Now().Unix(),
	}

	data, _ := json.Marshal(message)
	wsHub.broadcast <- data

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "文件推送成功",
		"count":   len(files),
	})
}

// PushNodeConfigFiles 批量推送拨测节点配置文件到所有客户端
func PushNodeConfigFiles(c *gin.Context) {
	var req struct {
		Files []struct {
			Path    string `json:"path" binding:"required"`
			Content string `json:"content" binding:"required"`
		} `json:"files" binding:"required"`
		Metadata map[string]string `json:"metadata"`
	}

	req.Files = []struct {
		Path    string `json:"path" binding:"required"`
		Content string `json:"content" binding:"required"`
	}{
		{Path: "blackbox_exporter.yaml", Content: "blackbox_exporter.yaml"},
		{Path: "prometheus.yml", Content: "prometheus.yml"},
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	var files []FileData
	for _, f := range req.Files {
		hash := md5.Sum([]byte(f.Content))
		files = append(files, FileData{
			Path:    f.Path,
			Content: f.Content,
			Hash:    hex.EncodeToString(hash[:]),
		})
	}

	message := FileMessage{
		Type:      "file_push",
		Files:     files,
		Metadata:  req.Metadata,
		Timestamp: time.Now().Unix(),
	}

	data, _ := json.Marshal(message)
	wsHub.broadcast <- data

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "文件推送成功",
		"count":   len(files),
	})
}

// GetConnectedClients 获取已连接的客户端列表
func GetConnectedClients(c *gin.Context) {
	wsHub.mutex.RLock()
	defer wsHub.mutex.RUnlock()

	var clients []map[string]string
	for client := range wsHub.clients {
		clients = append(clients, map[string]string{
			"client_id": client.clientID,
			"client_ip": client.clientIP,
			"version":   client.version,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"clients": clients,
		"count":   len(clients),
	})
}

// PushNodeAllConfigFiles 按客户端生成推送文件blackbox.yaml、nodeinfo.json、items.json到各个客户端
func PushNodeAllConfigFiles(c *gin.Context) {
	// 获取推送路径配置
	filesConfig := config.GetFilesConfig("")
	pushPath := filesConfig.PushPath
	// 1. 获取items数据
	itemsData, items, err := model.GetItemsData()
	if err != nil {
		log.Error("获取items数据失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取items数据失败",
			"error":   err.Error(),
		})
		return
	}

	itemsJson, err := json.Marshal(gin.H{
		"data":  itemsData,
		"count": len(itemsData),
	})
	if err != nil {
		log.Error("序列化items数据失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "生成items配置失败",
			"error":   err.Error(),
		})
		return
	}

	// 2. 为每个连接的客户端单独推送
	wsHub.mutex.RLock()
	clientCount := 0
	for client := range wsHub.clients {
		var clientIP string
		if client.clientIP != "" {
			clientIP = client.clientIP
		} else {
			// 从连接地址中提取IP
			clientIP = client.clientIP
			if colonIndex := strings.LastIndex(clientIP, ":"); colonIndex != -1 {
				clientIP = clientIP[:colonIndex]
			}
		}
		var nodeInfoJson []byte

		nodeData, err := model.GetNodeData(clientIP)
		if err != nil {
			log.Warnf("未找到客户端 %s 对应的node信息: %v", clientIP, err)
			// 如果找不到node信息，返回空数据
			nodeInfoJson, _ = json.Marshal(gin.H{
				"data": nil,
			})
		} else {
			nodeInfoJson, _ = json.Marshal(gin.H{
				"data": nodeData,
			})
		}

		// 3. 生成blackbox配置
		blackboxService := service.NewBlackboxService("")
		configContent, err := blackboxService.GenerateFromItemsContent(items, nodeData)
		if err != nil {
			log.Error("生成blackbox配置失败:", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "生成blackbox配置失败",
				"error":   err.Error(),
			})
			return
		}
		// 准备推送的文件
		itemsHash := md5.Sum(itemsJson)
		nodeInfoHash := md5.Sum(nodeInfoJson)
		blackboxHash := md5.Sum([]byte(configContent))

		files := []FileData{
			{
				Path:    fmt.Sprintf("%s/items.json", pushPath),
				Content: string(itemsJson),
				Hash:    hex.EncodeToString(itemsHash[:]),
			},
			{
				Path:    fmt.Sprintf("%s/nodeinfo.json", pushPath),
				Content: string(nodeInfoJson),
				Hash:    hex.EncodeToString(nodeInfoHash[:]),
			},
			{
				Path:    fmt.Sprintf("%s/blackbox.yaml", pushPath),
				Content: configContent,
				Hash:    hex.EncodeToString(blackboxHash[:]),
			},
		}

		message := FileMessage{
			Type:  "file_push",
			Files: files,
			Metadata: map[string]string{
				"type":      "node_config",
				"client_ip": clientIP,
				"timestamp": fmt.Sprintf("%d", time.Now().Unix()),
				"version":   "1.0.0",
			},
			Timestamp: time.Now().Unix(),
		}

		// 发送给特定客户端
		data, _ := json.Marshal(message)
		select {
		case client.send <- data:
			clientCount++
			log.Infof("成功推送配置文件到客户端 %s (%s)", client.clientID, clientIP)
		default:
			log.Warnf("推送配置文件到客户端 %s 失败，通道已满", client.clientID)
		}
	}
	wsHub.mutex.RUnlock()

	log.Infof("成功推送配置文件到 %d 个客户端", clientCount)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "配置文件推送成功",
		"files": []string{
			fmt.Sprintf("%s/items.json", pushPath),
			fmt.Sprintf("%s/nodeinfo.json", pushPath),
			fmt.Sprintf("%s/blackbox.yaml", pushPath),
		},
		"clients": clientCount,
	})
}

// pushConfigToSpecificNode 推送配置文件到特定节点
func pushConfigToSpecificNode(targetIP string) error {
	// 快速查找目标客户端
	wsHub.mutex.RLock()
	targetClient, exists := wsHub.clientsByIP[targetIP]
	wsHub.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("未找到IP为 %s 的连接客户端", targetIP)
	}

	// 生成配置数据
	configData, err := generateConfigData(targetIP)
	if err != nil {
		return fmt.Errorf("生成配置数据失败: %v", err)
	}

	// 发送配置
	return targetClient.sendConfigData(configData)
}

// generateConfigData 分离配置数据生成逻辑
func generateConfigData(targetIP string) (*ConfigData, error) {
	filesConfig := config.GetFilesConfig("")
	pushPath := filesConfig.PushPath

	// 1. 获取节点信息和DNS
	nodeData, err := model.GetNodeData(targetIP)
	if err != nil {
		return nil, fmt.Errorf("获取节点信息失败: %v", err)
	}
	var nodeInfoJson []byte
	nodeInfoJson, err = json.Marshal(gin.H{"data": nodeData})
	if err != nil {
		return nil, fmt.Errorf("序列化节点信息失败: %v", err)
	}

	// 2. 获取items数据
	itemsData, items, err := model.GetItemsData()
	if err != nil {
		return nil, fmt.Errorf("获取items数据失败: %v", err)
	}

	itemsJson, err := json.Marshal(gin.H{
		"data":  itemsData,
		"count": len(itemsData),
	})
	if err != nil {
		return nil, fmt.Errorf("序列化items数据失败: %v", err)
	}

	// 3. 生成blackbox配置，传入DNS服务器
	blackboxService := service.NewBlackboxService("")
	configContent, err := blackboxService.GenerateFromItemsContent(items, nodeData)
	if err != nil {
		return nil, fmt.Errorf("生成blackbox配置失败: %v", err)
	}

	// 4. 生成节点信息JSON

	return &ConfigData{
		PushPath:      pushPath,
		ItemsJson:     itemsJson,
		ConfigContent: configContent,
		NodeInfoJson:  nodeInfoJson,
		TargetIP:      targetIP,
	}, nil
}

type ConfigData struct {
	PushPath      string
	ItemsJson     []byte
	ConfigContent string
	NodeInfoJson  []byte
	TargetIP      string
}

func (c *WSClient) sendConfigData(configData *ConfigData) error {
	// 准备推送的文件
	itemsHash := md5.Sum(configData.ItemsJson)
	nodeInfoHash := md5.Sum(configData.NodeInfoJson)
	blackboxHash := md5.Sum([]byte(configData.ConfigContent))

	files := []FileData{
		{
			Path:    fmt.Sprintf("%s/items.json", configData.PushPath),
			Content: string(configData.ItemsJson),
			Hash:    hex.EncodeToString(itemsHash[:]),
		},
		{
			Path:    fmt.Sprintf("%s/nodeinfo.json", configData.PushPath),
			Content: string(configData.NodeInfoJson),
			Hash:    hex.EncodeToString(nodeInfoHash[:]),
		},
		{
			Path:    fmt.Sprintf("%s/blackbox.yaml", configData.PushPath),
			Content: configData.ConfigContent,
			Hash:    hex.EncodeToString(blackboxHash[:]),
		},
	}

	message := FileMessage{
		Type:  "file_push",
		Files: files,
		Metadata: map[string]string{
			"type":      "node_config",
			"client_ip": configData.TargetIP,
			"timestamp": fmt.Sprintf("%d", time.Now().Unix()),
			"version":   "1.0.0",
		},
		Timestamp: time.Now().Unix(),
	}

	return c.sendMessage(message)
}

// PushConfigToNode 推送配置到指定节点
func PushConfigToNode(c *gin.Context) {
	var req struct {
		TargetIP string `json:"target_ip" binding:"required"`
		Message  string `json:"message"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 推送配置到指定节点
	if err := pushConfigToSpecificNode(req.TargetIP); err != nil {
		log.Error("推送配置失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "推送配置失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "配置推送成功",
		"target":  req.TargetIP,
	})
}

// ReloadBlackboxConfig 重新加载指定节点的Blackbox配置
func ReloadBlackboxConfig(c *gin.Context) {
	var req struct {
		TargetIP string `json:"target_ip" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 查找目标客户端
	wsHub.mutex.RLock()
	targetClient, exists := wsHub.clientsByIP[req.TargetIP]
	wsHub.mutex.RUnlock()

	if !exists {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "未找到目标节点连接",
			"target":  req.TargetIP,
		})
		return
	}

	// 发送重载命令
	message := FileMessage{
		Type: "reload_blackbox",
		Metadata: map[string]string{
			"action":    "reload",
			"target_ip": req.TargetIP,
		},
		Timestamp: time.Now().Unix(),
	}

	if err := targetClient.sendMessage(message); err != nil {
		log.Error("发送重载命令失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "发送重载命令失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Blackbox配置重载命令已发送",
		"target":  req.TargetIP,
	})
}

// getRealClientIP 获取客户端真实IP地址
func getRealClientIP(c *gin.Context) string {
	// 优先级顺序检查各种代理头
	headers := []string{
		"X-Forwarded-For",
		"X-Real-IP",
		"X-Original-Forwarded-For",
		"CF-Connecting-IP",
		"True-Client-IP",
	}

	for _, header := range headers {
		ip := c.GetHeader(header)
		if ip != "" {
			// X-Forwarded-For 可能包含多个IP，取第一个
			if header == "X-Forwarded-For" {
				ips := strings.Split(ip, ",")
				if len(ips) > 0 {
					return strings.TrimSpace(ips[0])
				}
			}
			return ip
		}
	}

	// 如果没有代理头，使用RemoteAddr
	return c.ClientIP()
}
