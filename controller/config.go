package controller

import (
	"boce-service/model"
	"boce-service/service"
	"boce-service/utils/log"
	"encoding/base64"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"net/http"
)

// ConfigResponse 统一配置响应结构
type ConfigResponse struct {
	BlackboxYaml   Blackbox       `json:"blackbox_yaml"`
	ItemsJson      Items          `json:"items_json"`
	NodeInfoJson   model.NodeData `json:"nodeinfo_json"`
	VmagentYaml    Vmagent        `json:"vmagent_yaml"`
	BlackboxBase64 string         `json:"blackbox_base64"`
	VmagentBase64  string         `json:"vmagent_base64"`
}

type Blackbox struct {
	ConfigBase64  string `json:"config_base64"`
	ConfigContent string `json:"config_content"`
	Count         int    `json:"count"`
}

type Items struct {
	Count int              `json:"count"`
	Data  []model.ItemData `json:"data"`
}

type Vmagent struct {
	ConfigContent string `json:"config_content"`
	ConfigBase64  string `json:"config_base64"`
}

// GenerateAllConfigs 生成所有配置文件
func GenerateAllConfigs(c *gin.Context) {
	var clientIP string
	if c.Query("ip") != "" {
		clientIP = c.Query("ip")
		log.Info("clientIP:", c.Query("ip"))
	} else {
		clientIP = c.ClientIP()
		log.Info("clientIP:", clientIP)
	}

	response, err := GetGenerateAllConfigs(clientIP)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "生成配置失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "配置生成成功",
		"data":    response,
	})
}

func GetGenerateAllConfigs(clientIP string) (ConfigResponse, error) {
	response := ConfigResponse{}

	// 1. 生成nodeinfo.json配置
	nodeData, err := model.GetNodeData(clientIP)
	if err != nil {
		log.Error("获取节点信息失败:", err)
		return ConfigResponse{}, err
	}
	response.NodeInfoJson = nodeData

	// 2. 生成items.json配置
	itemsData, items, err := model.GetItemsData()
	if err != nil {
		log.Error("获取Items失败:", err)
		return ConfigResponse{}, err
	}
	response.ItemsJson = Items{
		Count: len(itemsData),
		Data:  itemsData,
	}

	// 3. 生成blackbox.yaml配置
	blackboxService := service.NewBlackboxService("")
	blackboxContent, err := blackboxService.GenerateFromItemsContent(items, nodeData)
	if err != nil {
		log.Error("生成blackbox配置失败:", err)
		return ConfigResponse{}, err
	}
	response.BlackboxYaml.ConfigContent = blackboxContent

	blackboxBase64, err := blackboxService.GenerateFromItemsBase64(items, nodeData)
	if err != nil {
		log.Error("生成blackbox base64配置失败:", err)
	} else {
		response.BlackboxBase64 = blackboxBase64
		response.BlackboxYaml.Count = len(items)
		response.BlackboxYaml.ConfigBase64 = blackboxBase64
	}

	// 4. 生成vmagent.yaml配置
	vmagentService := service.NewVMAgentService("")
	var modules []model.BlackboxModule
	for _, item := range items {
		if item.Status != model.ITEM_ON_STATUS {
			continue
		}

		module := blackboxService.ConvertItemToModule(item, nodeData)
		if module != nil {
			modules = append(modules, *module)
		}
	}

	vmagentContent, err := vmagentService.GenerateFromItemAndNodeData(items, nodeData)
	if err != nil {
		log.Error("生成vmagent配置失败:", err)
		return ConfigResponse{}, err
	}

	response.VmagentBase64 = base64.StdEncoding.EncodeToString([]byte(vmagentContent))
	response.VmagentYaml = Vmagent{
		ConfigContent: vmagentContent,
		ConfigBase64:  response.VmagentBase64,
	}
	return response, nil
}

// generateItemsJson 生成items.json配置
func generateItemsJson(items []model.Item) (string, error) {
	var itemsData []model.ItemData
	for _, item := range items {
		if item.Status != model.ITEM_ON_STATUS {
			continue
		}

		itemData := model.ItemData{
			Id:         item.Id,
			Name:       item.Name,
			Target:     item.Target,
			Method:     item.Method,
			Owner:      item.Owner,
			ItemData:   item.ItemData,
			Interval:   item.Interval,
			Status:     item.Status,
			IsAdvanced: item.IsAdvanced,
		}
		itemsData = append(itemsData, itemData)
	}

	jsonData, err := json.MarshalIndent(itemsData, "", "  ")
	if err != nil {
		return "", err
	}

	return string(jsonData), nil
}

// generateNodeInfoJson 生成nodeinfo.json配置
func generateNodeInfoJson(nodeData model.NodeData) (string, error) {
	jsonData, err := json.MarshalIndent(nodeData, "", "  ")
	if err != nil {
		return "", err
	}

	return string(jsonData), nil
}

// GenerateItemsJson 生成items.json配置（HTTP处理）
func GenerateItemsJson(c *gin.Context) {
	// 获取Items数据
	_, items, err := model.GetItemsData()
	if err != nil {
		log.Error("获取Items失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取Items失败",
		})
		return
	}

	itemsJson, err := generateItemsJson(items)
	if err != nil {
		log.Error("生成items.json失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "生成items.json失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "items.json生成成功",
		"data":    itemsJson,
		"base64":  base64.StdEncoding.EncodeToString([]byte(itemsJson)),
	})
}

// GenerateNodeInfoJson 生成nodeinfo.json配置（HTTP处理）
func GenerateNodeInfoJson(c *gin.Context) {
	var clientIP string
	if c.Query("ip") != "" {
		clientIP = c.Query("ip")
		log.Info("clientIP:", c.Query("ip"))
	} else {
		clientIP = c.ClientIP()
		log.Info("clientIP:", clientIP)
	}

	// 获取节点信息
	nodeData, err := model.GetNodeData(clientIP)
	if err != nil {
		log.Error("获取节点信息失败:", err)
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "节点不存在",
		})
		return
	}

	nodeInfoJson, err := generateNodeInfoJson(nodeData)
	if err != nil {
		log.Error("生成nodeinfo.json失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "生成nodeinfo.json失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "nodeinfo.json生成成功",
		"data":    nodeInfoJson,
		"base64":  base64.StdEncoding.EncodeToString([]byte(nodeInfoJson)),
	})
}
