package controller

import (
	"boce-service/model"
	"boce-service/service"
	"boce-service/utils/log"
	"encoding/base64"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
)

// GenerateBlackboxConfigData 生成blackbox配置数据（业务逻辑）
func GenerateBlackboxConfigData(nodeData model.NodeData) (map[string]interface{}, error) {
	blackboxService := service.NewBlackboxService("")
	items := model.GetItemsByStatus(model.ITEM_ON_STATUS)

	log.Infof("找到 %d 条启用的Item记录", len(items))

	configBase64, err := blackboxService.GenerateFromItemsBase64(items, nodeData)
	if err != nil {
		return nil, fmt.Errorf("生成blackbox配置失败: %v", err)
	}

	configContent, _ := blackboxService.GenerateFromItemsContent(items, nodeData)

	return map[string]interface{}{
		"config_base64":  configBase64,
		"config_preview": "\n" + configContent,
		"count":          len(items),
	}, nil
}

// GenerateBlackboxConfig 生成blackbox_exporter配置并返回base64字符串（HTTP处理）
func GenerateBlackboxConfig(c *gin.Context) {
	var clientIP string
	if c.Query("ip") != "" {
		clientIP = c.Query("ip")
		log.Info("clientIP:", c.Query("ip"))

	} else {
		clientIP = c.ClientIP()
		log.Info("clientIP:", clientIP)
	}

	nodeData, err := model.GetNodeData(clientIP)

	data, err := GenerateBlackboxConfigData(nodeData)
	if err != nil {
		log.Error(err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "生成配置失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":           200,
		"message":        "配置生成成功",
		"config_base64":  data["config_base64"],
		"config_preview": data["config_preview"],
		"count":          data["count"],
	})
}

// GenerateVMAgentConfig 生成vmagent配置
func GenerateVMAgentConfig(c *gin.Context) {
	nodeIP := c.Query("ip")
	if nodeIP == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "缺少ip参数",
		})
		return
	}

	// 获取节点信息
	nodeData, err := model.GetNodeData(nodeIP)
	if err != nil {
		log.Error("获取节点信息失败:", err)
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "节点不存在",
		})
		return
	}

	// 获取该节点的Items
	_, items, err := model.GetItemsData()
	if err != nil {
		log.Error("获取Items失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取Items失败",
		})
		return
	}
	var modules []model.BlackboxModule
	// 生成blackbox配置
	blackboxService := service.NewBlackboxService("")
	for _, item := range items {
		if item.Status != model.ITEM_ON_STATUS {
			continue
		}

		module := blackboxService.ConvertItemToModule(item, nodeData)
		if module != nil {
			modules = append(modules, *module)
		}
	}

	// 生成vmagent配置
	vmagentService := service.NewVMAgentService("")
	configContent, err := vmagentService.GenerateFromItemAndNodeData(items, nodeData)
	if err != nil {
		log.Error("生成vmagent配置失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "生成vmagent配置失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":           200,
		"message":        "vmagent配置生成成功",
		"config_content": configContent,
	})
}

// GenerateBlackboxAndVMAgentConfigs 生成所有配置（blackbox + vmagent）
func GenerateBlackboxAndVMAgentConfigs(c *gin.Context) {
	nodeIP := c.Query("ip")
	if nodeIP == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "缺少ip参数",
		})
		return
	}

	// 获取节点信息
	nodeData, err := model.GetNodeData(nodeIP)
	if err != nil {
		log.Error("获取节点信息失败:", err)
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "节点不存在",
		})
		return
	}

	// 获取该节点的Items
	_, items, err := model.GetItemsData()
	if err != nil {
		log.Error("获取Items失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取Items失败",
		})
		return
	}

	// 生成blackbox配置
	blackboxService := service.NewBlackboxService("")
	blackboxConfigContent, err := blackboxService.GenerateFromItemsContent(items, nodeData)
	if err != nil {
		log.Error("生成blackbox模块失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "生成blackbox配置失败",
		})
		return
	}

	blackboxConfigBase64, err := blackboxService.GenerateFromItemsBase64(items, nodeData)
	if err != nil {
		log.Error("生成blackbox配置失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "生成blackbox配置失败",
		})
		return
	}

	// 生成vmagent配置
	vmagentService := service.NewVMAgentService("")
	var modules []model.BlackboxModule
	for _, item := range items {
		if item.Status != model.ITEM_ON_STATUS {
			continue
		}

		module := blackboxService.ConvertItemToModule(item, nodeData)
		if module != nil {
			modules = append(modules, *module)
		}
	}
	vmagentConfigContent, err := vmagentService.GenerateFromItemAndNodeData(items, nodeData)
	if err != nil {
		log.Error("生成vmagent配置失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "生成vmagent配置失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "所有配置生成成功",
		"data": gin.H{
			"blackbox": gin.H{
				"config_base64":  blackboxConfigBase64,
				"config_content": blackboxConfigContent,
				"modules":        len(modules),
			},
			"vmagent": gin.H{
				"config_content": vmagentConfigContent,
			},
		},
	})
}

// DecodeBlackboxConfig 解码base64配置字符串
func DecodeBlackboxConfig(c *gin.Context) {
	var req struct {
		ConfigBase64 string `json:"config_base64" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数格式错误",
		})
		return
	}

	// 解码base64
	configContent, err := base64.StdEncoding.DecodeString(req.ConfigBase64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "base64解码失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":           200,
		"message":        "配置解码成功",
		"config_content": string(configContent),
	})
}
