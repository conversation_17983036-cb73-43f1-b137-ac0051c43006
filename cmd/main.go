package main

import (
	"boce-service/config"
	"boce-service/controller"
	"boce-service/model"
	"boce-service/router"
	"boce-service/utils/log"
	"flag"
	"fmt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"os"
)

var DB *gorm.DB

func InitDB(configPath string) {
	var err error
	dbConfig := config.GetDatabaseConfig(configPath)

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local&interpolateParams=true",
		dbConfig.Username,
		dbConfig.Password,
		dbConfig.Host,
		dbConfig.Port,
		dbConfig.DBName)

	log.Info("正在连接数据库...")
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
		panic(err)
	}

	// 设置模型的数据库连接
	model.SetDB(DB)
	log.Info("数据库连接成功")

	if os.Getenv("AUTO_MIGRATE") == "true" {
		log.Info("开始自动迁移数据库表结构...")
		err = DB.AutoMigrate(&model.DNS{}, &model.Node{}, &model.Item{})
		if err != nil {
			log.Fatalf("数据库迁移失败: %v", err)
			panic(err)
		}
		log.Info("数据库迁移完成")
	}
}

func main() {
	// 定义命令行参数
	var configPath = flag.String("config", "", "配置文件路径 (例如: -config=/path/to/config.yaml)")
	var help = flag.Bool("help", false, "显示帮助信息")

	// 解析命令行参数
	flag.Parse()

	// 显示帮助信息
	if *help {
		fmt.Println("使用方法:")
		fmt.Println("  ./boce-service [选项]")
		fmt.Println("")
		fmt.Println("选项:")
		flag.PrintDefaults()
		fmt.Println("")
		fmt.Println("示例:")
		fmt.Println("  ./boce-service -config=config/prod.yaml")
		fmt.Println("  ./boce-service -config=/etc/boce/config.yaml")
		return
	}

	// 初始化日志
	logConfig := &log.Config{
		Level:    log.INFO,
		FilePath: "logs/app.log",
	}
	if err := log.Init(logConfig); err != nil {
		panic(err)
	}

	// 加载配置
	if err := config.LoadConfig(*configPath); err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	if *configPath != "" {
		log.Infof("使用配置文件: %s", *configPath)
	} else {
		log.Info("使用默认配置文件")
	}

	// 初始化数据库
	InitDB(*configPath)

	log.Info("服务启动中...")

	// 启动WebSocket Hub
	go controller.StartWSHub()
	log.Info("WebSocket Hub已启动")

	// 初始化路由
	r := router.InitRouter()

	// 启动服务
	serverConfig := config.GetServerConfig(*configPath)
	port := serverConfig.Port
	log.Infof("服务运行在 :%d", port)
	if err := r.Run(fmt.Sprintf(":%d", port)); err != nil {
		log.Fatalf("服务启动失败: %v", err)
	}
}
