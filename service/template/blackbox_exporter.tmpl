modules:
  http_get_default:
    prober: http
    timeout: 5s
    http:
      method: GET
      valid_http_versions: ["HTTP/1.1", "HTTP/2.0"]
      valid_status_codes: [200]
      no_follow_redirects: false
      dns_server: {{ .DefaultDNS }}
  http_post_default:
    prober: http
    timeout: 5s
    http:
      method: POST
      valid_http_versions: ["HTTP/1.1", "HTTP/2.0"]
      valid_status_codes: [200]
      no_follow_redirects: false
      dns_server: {{ .DefaultDNS }}
      headers:
        Content-Type: application/json
  tcp_default:
    prober: tcp
    timeout: 5s
    tcp:
      preferred_ip_protocol: ip4
  icmp_default:
    prober: icmp
    timeout: 5s
    icmp:
      preferred_ip_protocol: ip4
{{- range .Modules}}
  {{.Name}}:
    prober: {{.Prober}}
    timeout: 5s
    {{- if eq .Prober "http"}}
    http:
      method: {{.HTTP.Method}}
      valid_http_versions: ["{{ join .HTTP.ValidHTTPVersions  "\", \"" }}"]
      valid_status_codes: [{{ joinInt .HTTP.ValidStatusCodes  ", " }}]
      {{- if .HTTP.Headers}}
      headers:
        {{- range $key, $value := .HTTP.Headers}}
        {{$key}}: "{{$value}}"
        {{- end}}
      {{- end}}
      {{- if .HTTP.Body}}
      body: "{{.HTTP.Body}}"
      {{- end}}
      {{- if .HTTP.DnsServer}}
      dns_server: "{{.HTTP.DnsServer}}"
      {{- end}}
      no_follow_redirects: {{.HTTP.NoFollowRedirects}}
    {{- else if eq .Prober "tcp"}}
    tcp:
      {{- if .TCP.PreferredIPProtocol}}
      preferred_ip_protocol: "{{.TCP.PreferredIPProtocol}}"
      {{- end}}
      {{- if .TCP.QueryResponse}}
      query_response:
        {{- range .TCP.QueryResponse}}
        - expect: "{{.Expect}}"
          {{- if .Send}}
          send: "{{.Send}}"
          {{- end}}
        {{- end}}
      {{- end}}
      {{- if .TCP.TLSConfig}}
      tls_config:
        insecure_skip_verify: {{.TCP.TLSConfig.InsecureSkipVerify}}
      {{- end}}
    {{- else if eq .Prober "icmp"}}
    icmp:
      {{- if .ICMP.PreferredIPProtocol}}
      preferred_ip_protocol: "{{.ICMP.PreferredIPProtocol}}"
      {{- end}}
    {{- else if eq .Prober "dns"}}
    dns:
      {{- if .DNS.QueryName}}
      query_name: "{{.DNS.QueryName}}"
      {{- end}}
      {{- if .DNS.QueryType}}
      query_type: "{{.DNS.QueryType}}"
      {{- end}}
      {{- if .DNS.ValidRcodes}}
      valid_rcodes: [{{ join .DNS.ValidRcodes   ", "}}]
      {{- end}}
      {{- if .DNS.ValidateAnswer}}
      validate_answer_rrs:
        {{- if .DNS.ValidateAnswer.FailIfMatchesRegexp}}
        fail_if_matches_regexp: ["{{ join .DNS.ValidateAnswer.FailIfMatchesRegexp "\", \""}}"]
        {{- end}}
        {{- if .DNS.ValidateAnswer.FailIfNotMatchesRegexp}}
        fail_if_not_matches_regexp: ["{{ join .DNS.ValidateAnswer.FailIfNotMatchesRegexp "\", \""}}"]
        {{- end}}
      {{- end}}
    {{- end}}
{{- end}}