# VMAgent Configuration
# Generated automatically

global:
  scrape_interval: {{ .GlobalConfig.ScrapeInterval }}
  evaluation_interval: {{ .GlobalConfig.EvaluationInterval }}

scrape_configs:
{{- range .ScrapeConfigs }}
  - job_name: {{ quote .JobName }}
    {{- if .ScrapeInterval }}
    scrape_interval: {{ .ScrapeInterval }}
    {{- end }}
    {{- if .MetricsPath }}
    metrics_path: {{ .MetricsPath }}
    {{- end }}
    {{- if .Params }}
    params:
      {{- range $key, $values := .Params }}
      {{ $key }}:
        {{- range $values }}
        - {{ quote . }}
        {{- end }}
      {{- end }}
    {{- end }}
    {{- if .StaticConfigs }}
    static_configs:
      {{- range .StaticConfigs }}
      - targets:
          {{- range .Targets }}
          - {{ quote . }}
          {{- end }}
        {{- if .Labels }}
        labels:
          {{- range $key, $value := .Labels }}
          {{ $key }}: {{ quote $value }}
          {{- end }}
        {{- end }}
      {{- end }}
    {{- end }}
    {{- if .RelabelConfigs }}
    relabel_configs:
      {{- range .RelabelConfigs }}
      - {{- if .SourceLabels }} source_labels:  [ {{- range .SourceLabels }} "{{ . }}" {{- end }} ] {{- end }}
        {{- if .TargetLabel }} target_label: "{{ .TargetLabel }}" {{- end }}
        {{- if .Regex }} regex: "{{ .Regex }}" {{- end }}
        {{- if .Replacement }} replacement: "{{ .Replacement }}" {{- end }}
        {{- if .Action }} action: "{{ .Action }}" {{- end }}
      {{- end }}
    {{- end }}
{{- end }}

{{- if .RemoteWrite }}
remote_write:
  {{- range .RemoteWrite }}
  - url: {{ quote .URL }}
    {{- if .Name }}
    name: {{ quote .Name }}
    {{- end }}
  {{- end }}
{{- end }}
