# VMAgent Configuration
# Generated automatically

global:
  scrape_interval: {{ .GlobalConfig.ScrapeInterval }}
  evaluation_interval: {{ .GlobalConfig.EvaluationInterval }}


scrape_configs:
- job_name: "blackbox_http_get_default"
  scrape_interval: 61s
  metrics_path: /probe
  params:
    module:
    - "http_get_default"
  static_configs:
    - targets:
      - "www.yy.com"
      labels:
        isp_code: {{ .nodeData.IspCode }}
        latitude: {{ .nodeData.Latwgs }}
        longitude: {{ .nodeData.Lngwgs }}
        method: {{ .nodeData.Method }}
        module: "www.yy.com_dns_custom"
        node_city: "广州市"
        node_ip: "***********"
        node_isp: "中国电信"
        node_province: "广东省"
        prober: "dns"
        zip_code: {{ .nodeData.Method }}
  relabel_configs:
  - source_labels: __address__
    target_label: __param_target
  - source_labels: __param_target
    target_label: instance
  - target_label: __address__
    replacement: 127.0.0.1:9115

{{- range .ScrapeConfigs }}
- job_name: {{ quote .JobName }}
  {{- if .ScrapeInterval }}
  scrape_interval: {{ .ScrapeInterval }}
  {{- end }}
  metrics_path: /probe
  params:
  {{- range  .Params.Modules}}
    module:
    - "{{ . }}"
  {{- end }}
  {{- range  .StaticConfigs}}
  static_configs:
  - targets:
    {{- range  .Targets}}
    - "{{ . }}"
    {{- end }}
    {{- end }}
    labels:
      isp_code: "CTL"
      latitude: "22.930876"
      longitude: "113.404840"
      method: "dns"
      module: "www.yy.com_dns_custom"
      node_city: "广州市"
      node_ip: "***********"
      node_isp: "中国电信"
      node_province: "广东省"
      prober: "dns"
      zip_code: "511400"
  relabel_configs:
  - source_labels: __address__
    target_label: __param_target
  - source_labels: __param_target
    target_label: instance
  - target_label: __address__
    replacement: 127.0.0.1:9115
{{- end }}
