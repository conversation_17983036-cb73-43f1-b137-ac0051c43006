package service

import (
	"boce-service/utils/log"
	"bytes"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"net"
	"os"
	"strings"
	"time"
)

type AlarmService struct {
	templatePath string
	outputPath   string
}

const (
	FID_URI         = (3001<<8 | 11)
	ALARM_LOGID     = 1
	UNIX_PATH       = "/tmp/yymp.agent.sock"
	Fid             = 45174
	Sid             = 144292
	AlarmSid        = 153581
	WarnSid         = 153582
	NoticeSid       = 155680
	CommonFid       = 45170
	EventAlarmSid   = 181926
	EventFid        = 45708
	BizFid          = 45902
	ClusterScaleSid = 375645
)

type AlarmTag struct {
	Id         int     `json:"id"`
	StrategyId int     `json:"sid"`
	Pre        int     `json:"pre"`
	Value      float64 `json:"value"`
	Pname      string  `json:"pname,omitempty"`
	Pid        int     `json:"pid,omitempty"`
	Msg        string  `json:"msg"`
	RoomId     int     `json:"room_id,omitempty"`
	RoomName   string  `json:"room_name,omitempty"`
	Alarm      int     `json:"alarm,omitempty"`
	AlarmLevel int     `json:"alarm_level,omitempty"`
	Ip         string  `json:"ip"`
	ProxyAlarm int     `json:"proxy_alarm"`
	OpAdminDw  string  `json:"op_admin_dw,omitempty"`
	AdminDw    string  `json:"admin_dw,omitempty"`
	BussName   string  `json:"buss_name,omitempty"`
	Context    string  `json:"context"`
	MsgKey     string  `json:"msg_key"`
	Defined    int     `json:"defined,omitempty"`
}

type RoomInfo struct {
	RoomId     int    `json:"room_id,omitempty"`
	RoomName   string `json:"room_name,omitempty"`
	Ip         string `json:"ip"`
	AlertType  string `json:"-"`
	RuleLevel  bool   `json:"force_follow_rule_level,omitempty"` // 为true则不跟随机器
	Defined    int    `json:"defined"`
	AlarmLevel int    `json:"alarm_level,omitempty"`
}

type AlarmContext struct {
	AppName        string `json:"appName"`
	ServiceName    string `json:"serviceName"`
	ServiceKey     string `json:"serviceKey"`
	PodName        string `json:"podName"`
	DeploymentName string `json:"deploymentName"`
	RowKey         string `json:"rowKey"`
}

func packMsgToJson(id, sid int, msg, pname, admin, opAdmin, bussName, context, msgKey string, info RoomInfo) (string, error) {
	alarmTag := AlarmTag{Id: id, StrategyId: sid, Pname: pname, Pid: os.Getpid(), BussName: bussName, Msg: msg, Alarm: 1, AdminDw: admin, OpAdminDw: opAdmin, Context: context, MsgKey: msgKey}
	if info.RoomId != 0 {
		alarmTag.RoomId = info.RoomId
		alarmTag.RoomName = info.RoomName
	}
	if info.Ip != "" {
		alarmTag.Ip = info.Ip
		alarmTag.ProxyAlarm = 1
		if info.RuleLevel {
			alarmTag.ProxyAlarm = 0
		}
		if opAdmin != "" {
			alarmTag.ProxyAlarm = 2
		}
	}
	if info.AlarmLevel > 0 {
		alarmTag.AlarmLevel = info.AlarmLevel
	}
	alarmTag.Defined = info.Defined
	alarmByts, err := json.Marshal(alarmTag)
	if err != nil {
		return "", err
	}

	return string(alarmByts), nil
}

func packAlarmToProto(alarmJson string) []byte {
	var buffSend bytes.Buffer
	tmNow := time.Now()

	packLen := 30 + len(alarmJson)

	//len
	binary.Write(&buffSend, binary.LittleEndian, int32(packLen))
	//uri
	binary.Write(&buffSend, binary.LittleEndian, int32(FID_URI))
	//code
	binary.Write(&buffSend, binary.LittleEndian, int16(200))
	//logid
	binary.Write(&buffSend, binary.LittleEndian, int64(ALARM_LOGID))
	//type
	binary.Write(&buffSend, binary.LittleEndian, int16(0))
	//timestamp
	binary.Write(&buffSend, binary.LittleEndian, int64(tmNow.UnixNano()/1000))
	//payload len
	binary.Write(&buffSend, binary.LittleEndian, int16(len(alarmJson)))
	//msg
	buffSend.WriteString(alarmJson)

	return buffSend.Bytes()
}

func SendAlarm(id, sid int, msg, pname, msgKey, admin, opAdmin string) error {
	log.Info("[Alarm][%d][%d][admin:%s][opAdmin:%s][%s][%s]", id, sid, admin, opAdmin, pname, msg)
	if len(msg) == 0 {
		return fmt.Errorf("invalid msg")
	}

	jsonStr, err := packMsgToJson(id, sid, msg, pname, admin, opAdmin, "运维ITIL->运维研发->容器平台[Container]", "{}", msgKey, RoomInfo{})
	if err != nil {
		return err
	}

	protoByts := packAlarmToProto(jsonStr)

	conn, err := net.DialTimeout("unixgram", UNIX_PATH, time.Duration(5)*time.Second)
	if err != nil {
		return err
	}

	defer conn.Close()

	_, err = conn.Write(protoByts)
	if err != nil {
		return err
	}

	return nil
}

func SendWebHookAlarm(id, sid int, msg, pname, msgKey, admin, opAdmin string, context AlarmContext, roomInfo RoomInfo) error {
	log.Info("[Alarm][%d][%d][%s][%s]", id, sid, pname, msg)
	if len(msg) == 0 {
		return fmt.Errorf("invalid msg")
	}

	body, _ := json.Marshal(context)

	jsonStr, err := packMsgToJson(id, sid, msg, pname, admin, opAdmin, "运维ITIL->运维研发->容器平台[Container]", string(body), msgKey, roomInfo)
	if err != nil {
		return err
	}

	protoByts := packAlarmToProto(jsonStr)

	conn, err := net.DialTimeout("unixgram", UNIX_PATH, time.Duration(5)*time.Second)
	if err != nil {
		return err
	}

	defer conn.Close()

	_, err = conn.Write(protoByts)
	if err != nil {
		return err
	}

	return nil
}

func SendAlarmByAdmin(id, sid int, msg, pname, admin, opAdmin, projectName, productName, podName string, info RoomInfo) error {
	if admin == "" {
		admin = opAdmin
	}
	return SendDeployAlarmByAdmin(id, sid, msg, pname, admin, opAdmin, projectName, productName, "", podName, info)
}

func SendDeployAlarmByAdmin(id, sid int, msg, pname, admin, opAdmin, projectName, productName, deployName, podName string, info RoomInfo) error {
	log.Info("[Alarm][%d][%d][%s][%s]", id, sid, pname, msg)
	if len(msg) == 0 {
		return fmt.Errorf("invalid msg")
	}

	alarmContext := AlarmContext{
		AppName:        projectName,
		PodName:        podName,
		ServiceName:    "",
		DeploymentName: deployName,
		ServiceKey:     strings.Join([]string{"3", productName, projectName}, "@"),
		RowKey:         "",
	}
	alarmContextBytes, _ := json.Marshal(&alarmContext)
	bussName := ""

	msgKey := ""
	if podName != "" && info.AlertType != "" {
		msgKey = info.AlertType + "/" + podName
	}
	if deployName != "" && podName == "" {
		msgKey = info.AlertType + "/" + projectName + "/" + deployName
	}
	jsonStr, err := packMsgToJson(id, sid, msg, pname, admin, opAdmin, strings.ReplaceAll(bussName, " ", ""), string(alarmContextBytes), msgKey, info)
	if err != nil {
		return err
	}

	protoByts := packAlarmToProto(jsonStr)

	conn, err := net.DialTimeout("unixgram", UNIX_PATH, time.Duration(5)*time.Second)
	if err != nil {
		return err
	}

	defer conn.Close()

	_, err = conn.Write(protoByts)
	if err != nil {
		return err
	}

	return nil
}

func SendInternalAlarm(msg, admin string) error {
	return SendAlarmByAdmin(Fid, Sid, msg, "", admin, "", "aquaman", "aquaman", "", RoomInfo{})
}

func SendStrEventAlarm(msg, admin string) error {
	return SendAlarmByAdmin(EventFid, EventAlarmSid, msg, "", admin, "", "aquaman", "aquaman", "", RoomInfo{})
}
