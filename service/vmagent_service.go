package service

import (
	"boce-service/model"
	"boce-service/utils/log"
	"bytes"
	"embed"
	"encoding/base64"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"text/template"
)

//go:embed template/vmagent.tmpl
var vmagentTemplateFS embed.FS

// VMAgentService vmagent配置生成服务
type VMAgentService struct {
	templatePath string
	outputPath   string
}

// NewVMAgentService 创建vmagent服务实例
func NewVMAgentService(outputPath string) *VMAgentService {
	return &VMAgentService{
		templatePath: "template/vmagent.tmpl",
		outputPath:   outputPath,
	}
}

// VMAgentConfig vmagent配置结构
type VMAgentConfig struct {
	GlobalConfig         GlobalConfig          `yaml:"global"`
	ScrapeConfigs        []ScrapeConfig        `yaml:"scrape_configs"`
	RemoteWrite          []RemoteWrite         `yaml:"remote_write,omitempty"`
	DefaultScrapeConfigs []DefaultScrapeConfig `yaml:"default_scrape_configs,omitempty"`
}

type DefaultScrapeConfig struct {
	Target     string            `yaml:"target"`
	DefaultJob string            `json:"defaultJob"`
	Labels     map[string]string `yaml:"labels,omitempty"`
}

// GlobalConfig 全局配置
type GlobalConfig struct {
	ScrapeInterval     string `yaml:"scrape_interval"`
	EvaluationInterval string `yaml:"evaluation_interval"`
}

// ScrapeConfig 抓取配置
type ScrapeConfig struct {
	JobName        string              `yaml:"job_name"`
	ScrapeInterval string              `yaml:"scrape_interval,omitempty"`
	MetricsPath    string              `yaml:"metrics_path,omitempty"`
	Params         map[string][]string `yaml:"params,omitempty"`
	StaticConfigs  []StaticConfig      `yaml:"static_configs,omitempty"`
	RelabelConfigs []RelabelConfig     `yaml:"relabel_configs,omitempty"`
}

// StaticConfig 静态配置
type StaticConfig struct {
	Targets []string          `yaml:"targets"`
	Labels  map[string]string `yaml:"labels,omitempty"`
}

// RelabelConfig 重标签配置
type RelabelConfig struct {
	SourceLabels []string `yaml:"source_labels,omitempty"`
	TargetLabel  string   `yaml:"target_label,omitempty"`
	Regex        string   `yaml:"regex,omitempty"`
	Replacement  string   `yaml:"replacement,omitempty"`
	Action       string   `yaml:"action,omitempty"`
}

// RemoteWrite 远程写入配置
type RemoteWrite struct {
	URL  string `yaml:"url"`
	Name string `yaml:"name,omitempty"`
}

// GenerateFromBlackboxModules 从blackbox模块生成vmagent配置
func (vs *VMAgentService) GenerateFromItemAndNodeData(items []model.Item, nodeData model.NodeData) (string, error) {
	config := VMAgentConfig{
		GlobalConfig: GlobalConfig{
			ScrapeInterval:     "30s",
			EvaluationInterval: "30s",
		},
	}

	// 生成blackbox_exporter抓取配置
	blackboxConfig := vs.generateBlackboxScrapeConfig(items, nodeData)
	config.ScrapeConfigs = append(config.ScrapeConfigs, blackboxConfig...)

	// 生成node_exporter抓取配置（如果需要）
	nodeExporterConfig := vs.generateNodeExporterScrapeConfig(nodeData)
	if nodeExporterConfig != nil {
		config.ScrapeConfigs = append(config.ScrapeConfigs, *nodeExporterConfig)
	}

	return vs.generateConfigContent(config)
}

// generateBlackboxScrapeConfig 生成blackbox_exporter的抓取配置
func (vs *VMAgentService) generateBlackboxScrapeConfig(items []model.Item, nodeData model.NodeData) []ScrapeConfig {
	var configs []ScrapeConfig

	// 按类型分组默认配置的targets
	defaultTargets := make(map[string][]string)

	for _, item := range items {
		if item.Status != model.ITEM_ON_STATUS {
			continue
		}

		target := item.Target

		if item.IsAdvanced == model.ITEM_ADVANCED_CONFIG {
			// 高级配置：根据item和node信息生成job_name
			var jobName string
			var moduleName string
			jobName = fmt.Sprintf("%s_%s_%s_custom", vs.cleanTargetForJobName(target), item.Method)
			moduleName = fmt.Sprintf("%s_%s_custom", vs.cleanTargetForJobName(target), item.Method)

			config := ScrapeConfig{
				JobName:        jobName,
				ScrapeInterval: fmt.Sprintf("%ds", item.Interval),
				MetricsPath:    "/probe",
				Params: map[string][]string{
					"module": {moduleName},
				},
				StaticConfigs: []StaticConfig{
					{
						Targets: []string{target},
						Labels: map[string]string{
							"node_ip":       nodeData.IpAddr,
							"node_city":     nodeData.City,
							"node_province": nodeData.Province,
							"node_isp":      nodeData.Isp,
							"isp_code":      nodeData.IspCode,
							"zip_code":      nodeData.ZipCode,
							"longitude":     nodeData.Lngwgs,
							"latitude":      nodeData.Latwgs,
							"prober":        vs.getProberName(item.Method),
							"module":        moduleName,
							"method":        item.Method,
						},
					},
				},
				RelabelConfigs: []RelabelConfig{
					{
						SourceLabels: []string{"__address__"},
						TargetLabel:  "__param_target",
					},
					{
						SourceLabels: []string{"__param_target"},
						TargetLabel:  "instance",
					},
					{
						TargetLabel: "__address__",
						Replacement: "127.0.0.1:9115",
					},
				},
			}
			configs = append(configs, config)

		} else {
			// 默认配置：按方法类型分组
			var jobKey string
			switch item.Method {
			case model.ITEM_HTTP_GET_Method:
				jobKey = "http_get_default"
			case model.ITEM_HTTP_POST_Method:
				jobKey = "http_post_default"
			case model.ITEM_TCP_Method:
				jobKey = "tcp_default"
			case model.ITEM_ICMP_Method:
				jobKey = "icmp_default"
			case model.ITEM_DNS_Method:
				jobKey = "dns_default"
			default:
				continue
			}
			defaultTargets[jobKey] = append(defaultTargets[jobKey], target)
		}
	}

	// 生成默认配置的job
	for jobName, targets := range defaultTargets {
		if len(targets) == 0 {
			continue
		}
		var moduleName string
		moduleName = jobName
		jobName = fmt.Sprintf("blackbox_%s", jobName)

		config := ScrapeConfig{
			JobName:        jobName,
			ScrapeInterval: "30s",
			MetricsPath:    "/probe",
			Params: map[string][]string{
				"module": {moduleName},
			},
			StaticConfigs: []StaticConfig{
				{
					Targets: targets,
					Labels: map[string]string{
						"node_ip":       nodeData.IpAddr,
						"node_city":     nodeData.City,
						"node_province": nodeData.Province,
						"node_isp":      nodeData.Isp,
						"isp_code":      nodeData.IspCode,
						"zip_code":      nodeData.ZipCode,
						"longitude":     nodeData.Lngwgs,
						"latitude":      nodeData.Latwgs,
						"prober":        vs.getProberFromJobName(jobName),
						"module":        moduleName,
						"method":        vs.getMethodFromJobName(jobName),
					},
				},
			},
			RelabelConfigs: []RelabelConfig{
				{
					SourceLabels: []string{"__address__"},
					TargetLabel:  "__param_target",
				},
				{
					SourceLabels: []string{"__param_target"},
					TargetLabel:  "instance",
				},
				{
					TargetLabel: "__address__",
					Replacement: "127.0.0.1:9115",
				},
			},
		}
		configs = append(configs, config)
	}

	return configs
}

// cleanTargetForJobName 清理目标名称用于job名称
func (vs *VMAgentService) cleanTargetForJobName(target string) string {
	cleanTarget := strings.ReplaceAll(target, "://", "_")
	cleanTarget = strings.ReplaceAll(cleanTarget, "/", "_")
	cleanTarget = strings.ReplaceAll(cleanTarget, ":", "_")
	cleanTarget = strings.ReplaceAll(cleanTarget, "?", "_")
	cleanTarget = strings.ReplaceAll(cleanTarget, "&", "_")
	cleanTarget = strings.ReplaceAll(cleanTarget, "=", "_")
	cleanTarget = strings.ReplaceAll(cleanTarget, ".", "_")

	if len(cleanTarget) > 30 {
		cleanTarget = cleanTarget[:30]
	}

	return cleanTarget
}

// getProberName 根据方法获取探测器名称
func (vs *VMAgentService) getProberName(method string) string {
	switch method {
	case model.ITEM_HTTP_GET_Method, model.ITEM_HTTP_POST_Method:
		return "http"
	case model.ITEM_TCP_Method:
		return "tcp"
	case model.ITEM_ICMP_Method:
		return "icmp"
	case model.ITEM_DNS_Method:
		return "dns"
	default:
		return "unknown"
	}
}

// getProberFromJobName 从job名称获取探测器名称
func (vs *VMAgentService) getProberFromJobName(jobName string) string {
	if strings.Contains(jobName, "http") {
		return "http"
	} else if strings.Contains(jobName, "tcp") {
		return "tcp"
	} else if strings.Contains(jobName, "icmp") {
		return "icmp"
	} else if strings.Contains(jobName, "dns") {
		return "dns"
	}
	return "unknown"
}

// getMethodFromJobName 从job名称获取方法名称
func (vs *VMAgentService) getMethodFromJobName(jobName string) string {
	if strings.Contains(jobName, "http_get") {
		return model.ITEM_HTTP_GET_Method
	} else if strings.Contains(jobName, "http_post") {
		return model.ITEM_HTTP_POST_Method
	} else if strings.Contains(jobName, "tcp") {
		return model.ITEM_TCP_Method
	} else if strings.Contains(jobName, "icmp") {
		return model.ITEM_ICMP_Method
	} else if strings.Contains(jobName, "dns") {
		return model.ITEM_DNS_Method
	}
	return "unknown"
}

// findTargetsForModule 查找使用指定模块的目标
func (vs *VMAgentService) findTargetsForModule(module model.BlackboxModule, items []model.Item) []string {
	var targets []string

	for _, item := range items {
		if item.Status != model.ITEM_ON_STATUS {
			continue
		}

		// 根据模块名称判断是否匹配
		if vs.isModuleMatchItem(module, item) {
			targets = append(targets, item.Target)
		}
	}
	return targets
}

// isModuleMatchItem 判断模块是否匹配Item
func (vs *VMAgentService) isModuleMatchItem(module model.BlackboxModule, item model.Item) bool {
	// 检查探测类型是否匹配
	switch item.Method {
	case model.ITEM_HTTP_GET_Method:
		if module.Prober != "http" || module.HTTP.Method != "GET" {
			return false
		}
	case model.ITEM_HTTP_POST_Method:
		if module.Prober != "http" || module.HTTP.Method != "POST" {
			return false
		}
	case model.ITEM_TCP_Method:
		if module.Prober != "tcp" {
			return false
		}
	case model.ITEM_ICMP_Method:
		if module.Prober != "icmp" {
			return false
		}
	case model.ITEM_DNS_Method:
		if module.Prober != "dns" {
			return false
		}
	default:
		return false
	}

	// 检查模块名称是否匹配
	if strings.Contains(module.Name, "_default") {
		// 默认模块：匹配所有使用默认配置的Item
		bs := NewBlackboxService("")
		return !bs.IsCustomConfig(item)
	} else if strings.Contains(module.Name, "_custom") {
		// 自定义模块：检查目标是否匹配
		cleanTarget := vs.cleanTargetForModuleName(item.Target)
		return strings.HasPrefix(module.Name, cleanTarget)
	}

	return false
}

// cleanTargetForModuleName 清理目标名称用于模块名称匹配
func (vs *VMAgentService) cleanTargetForModuleName(target string) string {
	cleanTarget := strings.ReplaceAll(target, "://", "_")
	cleanTarget = strings.ReplaceAll(cleanTarget, "/", "_")
	cleanTarget = strings.ReplaceAll(cleanTarget, ":", "_")
	cleanTarget = strings.ReplaceAll(cleanTarget, "?", "_")
	cleanTarget = strings.ReplaceAll(cleanTarget, "&", "_")
	cleanTarget = strings.ReplaceAll(cleanTarget, "=", "_")

	if len(cleanTarget) > 50 {
		cleanTarget = cleanTarget[:50]
	}

	return cleanTarget
}

// generateNodeExporterScrapeConfig 生成node_exporter抓取配置
func (vs *VMAgentService) generateNodeExporterScrapeConfig(nodeData model.NodeData) *ScrapeConfig {
	return &ScrapeConfig{
		JobName:        "node_exporter",
		ScrapeInterval: "30s",
		StaticConfigs: []StaticConfig{
			{
				Targets: []string{"127.0.0.1:9100"},
				Labels: map[string]string{
					"node_ip":   nodeData.IpAddr,
					"node_city": nodeData.City,
					"node_isp":  nodeData.Isp,
				},
			},
		},
	}
}

// generateConfigContent 生成vmagent配置内容
func (vs *VMAgentService) generateConfigContent(config VMAgentConfig) (string, error) {
	// 从嵌入的文件系统读取模板
	tmplContent, err := vmagentTemplateFS.ReadFile("template/vmagent.tmpl")
	if err != nil {
		return "", fmt.Errorf("读取vmagent模板文件失败: %v", err)
	}

	// 创建模板函数映射
	funcMap := template.FuncMap{
		"join": func(slice []string, sep string) string {
			return strings.Join(slice, sep)
		},
		"quote": func(s string) string {
			return fmt.Sprintf(`"%s"`, s)
		},
	}

	// 解析模板
	tmpl, err := template.New("vmagent").Funcs(funcMap).Parse(string(tmplContent))
	if err != nil {
		return "", fmt.Errorf("解析vmagent模板失败: %v", err)
	}

	// 执行模板到内存缓冲区
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, config); err != nil {
		return "", fmt.Errorf("执行vmagent模板失败: %v", err)
	}

	return buf.String(), nil
}

// GenerateConfigBase64 生成vmagent配置并返回base64字符串
func (vs *VMAgentService) GenerateConfigBase64(config VMAgentConfig) (string, error) {
	content, err := vs.generateConfigContent(config)
	if err != nil {
		return "", err
	}

	if vs.outputPath != "" {
		// 确保输出目录存在
		outputDir := filepath.Dir(vs.outputPath)
		if err := os.MkdirAll(outputDir, 0755); err != nil {
			log.Errorf("创建vmagent输出目录失败: %v", err)
		}

		// 创建输出文件
		if err := os.WriteFile(vs.outputPath, []byte(content), 0644); err != nil {
			log.Errorf("写入vmagent配置文件失败: %v", err)
		} else {
			log.Infof("成功生成vmagent配置文件: %s", vs.outputPath)
		}
	}

	// 返回base64编码的配置内容
	return base64.StdEncoding.EncodeToString([]byte(content)), nil
}

// SaveConfigToFile 保存配置到文件
func (vs *VMAgentService) SaveConfigToFile(content string, filename string) error {
	if filename == "" {
		filename = vs.outputPath
	}

	if filename == "" {
		return fmt.Errorf("未指定输出文件路径")
	}

	// 确保输出目录存在
	outputDir := filepath.Dir(filename)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("创建输出目录失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(filename, []byte(content), 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}

	log.Infof("成功保存vmagent配置文件: %s", filename)
	return nil
}
