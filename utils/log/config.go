package log

// Config 日志配置
type Config struct {
    Level    int    `yaml:"level"`    // 日志级别
    FilePath string `yaml:"filePath"` // 日志文件路径
}

// DefaultConfig 默认日志配置
func DefaultConfig() *Config {
    return &Config{
        Level:    INFO,
        FilePath: "",
    }
}

// Init 初始化日志
func Init(config *Config) error {
    if config == nil {
        config = DefaultConfig()
    }
    
    return Setup(config.Level, config.FilePath)
}