package log

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"runtime"
)

// 日志级别
const (
	DEBUG = iota
	INFO
	WARN
	ERROR
	FATAL
)

var (
	// 默认日志级别
	level = INFO

	// 日志前缀
	levelFlags = []string{"DEBUG", "INFO", "WARN", "ERROR", "FATAL"}

	// 默认日志输出
	output io.Writer = os.Stdout

	// 日志对象
	logger = log.New(output, "", log.LstdFlags)
)

// Setup 设置日志配置
func Setup(logLevel int, logFile string) error {
	level = logLevel

	// 如果指定了日志文件，则输出到文件
	if logFile != "" {
		dir := filepath.Dir(logFile)
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			if err := os.MkdirAll(dir, 0755); err != nil {
				return fmt.Errorf("创建日志目录失败: %v", err)
			}
		}

		f, err := os.OpenFile(logFile, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
		if err != nil {
			return fmt.Errorf("打开日志文件失败: %v", err)
		}

		output = io.MultiWriter(os.Stdout, f)
		logger.SetOutput(output)
	}

	return nil
}

// SetLevel 设置日志级别
func SetLevel(logLevel int) {
	level = logLevel
}

// 获取调用者信息
func caller() string {
	_, file, line, ok := runtime.Caller(3) // 改为3，因为增加了调用层级
	if !ok {
		return "???"
	}
	// 只保留文件名，不包含完整路径
	filename := filepath.Base(file)
	return fmt.Sprintf("%s:%d", filename, line)
}

// 格式化日志消息
func formatLog(level int, v ...interface{}) string {
	callerInfo := caller()
	return fmt.Sprintf("[%s] [%s] %s", levelFlags[level], callerInfo, fmt.Sprint(v...))
}

// 格式化日志消息（带格式）
func formatLogf(level int, format string, v ...interface{}) string {
	callerInfo := caller()
	return fmt.Sprintf("[%s] [%s] %s", levelFlags[level], callerInfo, fmt.Sprintf(format, v...))
}

// Debug 输出调试级别日志
func Debug(v ...interface{}) {
	if level <= DEBUG {
		logger.Println(formatLog(DEBUG, v...))
	}
}

// Debugf 输出调试级别日志（带格式）
func Debugf(format string, v ...interface{}) {
	if level <= DEBUG {
		logger.Println(formatLogf(DEBUG, format, v...))
	}
}

// Info 输出信息级别日志
func Info(v ...interface{}) {
	if level <= INFO {
		logger.Println(formatLog(INFO, v...))
	}
}

// Infof 输出信息级别日志（带格式）
func Infof(format string, v ...interface{}) {
	if level <= INFO {
		logger.Println(formatLogf(INFO, format, v...))
	}
}

// Warn 输出警告级别日志
func Warn(v ...interface{}) {
	if level <= WARN {
		logger.Println(formatLog(WARN, v...))
	}
}

// Warnf 输出警告级别日志（带格式）
func Warnf(format string, v ...interface{}) {
	if level <= WARN {
		logger.Println(formatLogf(WARN, format, v...))
	}
}

// Error 输出错误级别日志
func Error(v ...interface{}) {
	if level <= ERROR {
		logger.Println(formatLog(ERROR, v...))
	}
}

// Errorf 输出错误级别日志（带格式）
func Errorf(format string, v ...interface{}) {
	if level <= ERROR {
		logger.Println(formatLogf(ERROR, format, v...))
	}
}

// Fatal 输出致命错误日志并退出程序
func Fatal(v ...interface{}) {
	if level <= FATAL {
		logger.Fatalln(formatLog(FATAL, v...))
	}
}

// Fatalf 输出致命错误日志（带格式）并退出程序
func Fatalf(format string, v ...interface{}) {
	if level <= FATAL {
		logger.Fatalln(formatLogf(FATAL, format, v...))
	}
}
