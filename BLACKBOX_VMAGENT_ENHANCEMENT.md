# Blackbox Exporter 和 VMAgent 配置生成增强

## 🎯 功能概述

本次改造实现了两个主要功能：

1. **智能Blackbox配置生成**: 支持默认配置模板和自定义配置，按规则生成模块名称
2. **VMAgent配置自动生成**: 基于Blackbox配置自动生成VMAgent抓取配置并支持下发

## ✨ 核心特性

### 1. Blackbox配置智能生成

#### 配置模式
- **默认配置**: 使用 `{method}_default` 格式（如 `http_default`、`tcp_default`）
- **自定义配置**: 使用 `{target}_{method}_custom` 格式（如 `www.yy.com_http_custom`）

#### 自定义配置判断逻辑
系统会自动判断Item是否需要自定义配置：

**HTTP方法**:
- 有自定义Headers
- 有自定义Body
- 有自定义HTTP版本
- 有自定义状态码

**TCP方法**:
- 有自定义IP协议
- 有自定义查询响应

**ICMP方法**:
- 有自定义IP协议

**DNS方法**:
- 查询类型不是默认的"A"
- 有自定义验证规则
- 有多个有效返回码

#### 模块名称生成规则
```
默认配置:
- http_default
- http_post_default  
- tcp_default
- icmp_default
- dns_default

自定义配置:
- www.example.com_http_custom
- api.service.com_http_post_custom
- *************_tcp_custom
- dns.server.com_dns_custom
```

### 2. VMAgent配置自动生成

#### 配置结构
- **全局配置**: 抓取间隔、评估间隔
- **抓取配置**: 基于Blackbox模块自动生成
- **重标签配置**: 自动配置目标重写
- **节点标签**: 自动添加节点信息标签

#### 抓取任务生成
- 按探测类型分组生成抓取任务
- 自动匹配Item目标到对应模块
- 支持多目标抓取配置
- 自动添加节点元数据标签

## 🔧 技术实现

### 后端架构

#### 1. BlackboxService 增强
```go
// 核心方法
func (bs *BlackboxService) convertItemToModule(item model.Item, nodeData model.NodeData) *model.BlackboxModule
func (bs *BlackboxService) isCustomConfig(item model.Item) bool
func (bs *BlackboxService) generateDefaultModuleName(method string) string
func (bs *BlackboxService) generateCustomModuleName(target, method string) string

// 配置构建方法
func (bs *BlackboxService) buildDefaultHTTPProbe(nodeData model.NodeData, method string) *model.HTTPProbe
func (bs *BlackboxService) buildCustomHTTPProbe(item model.Item, nodeData model.NodeData, method string) *model.HTTPProbe
```

#### 2. VMAgentService 新增
```go
// 核心结构
type VMAgentService struct {
    templatePath string
    outputPath   string
}

// 主要方法
func (vs *VMAgentService) GenerateFromBlackboxModules(modules []model.BlackboxModule, items []model.Item, nodeData model.NodeData) (string, error)
func (vs *VMAgentService) generateBlackboxScrapeConfig(modules []model.BlackboxModule, items []model.Item, nodeData model.NodeData) []ScrapeConfig
```

### API接口

#### 新增接口
- `GET /api/vmagent/generate?ip={nodeIP}` - 生成VMAgent配置
- `GET /api/configs/generate/all?ip={nodeIP}` - 生成所有配置

#### 增强接口
- `GET /api/blackbox/generate/base64?ip={nodeIP}` - 支持智能配置生成

### 前端功能

#### 1. 配置查看功能
- 新增"查看配置"按钮
- 支持Blackbox和VMAgent配置预览
- 支持配置复制到剪贴板
- 支持配置推送到节点

#### 2. 配置管理界面
- 标签页切换查看不同配置
- 实时显示模块数量
- 配置内容语法高亮显示

## 📋 使用说明

### 1. 配置生成流程

#### 自动生成配置
1. 访问Node管理页面
2. 点击节点的"查看配置"按钮
3. 系统自动生成Blackbox和VMAgent配置
4. 在弹出窗口中查看配置内容

#### 手动API调用
```bash
# 生成Blackbox配置
curl "http://localhost:8080/api/blackbox/generate/base64?ip=*************"

# 生成VMAgent配置  
curl "http://localhost:8080/api/vmagent/generate?ip=*************"

# 生成所有配置
curl "http://localhost:8080/api/configs/generate/all?ip=*************"
```

### 2. 配置示例

#### Blackbox配置示例
```yaml
modules:
  http_default:
    prober: http
    timeout: 30s
    http:
      valid_http_versions: ["HTTP/1.1", "HTTP/2.0"]
      valid_status_codes: [200]
      method: GET
      
  www.example.com_http_custom:
    prober: http
    timeout: 30s
    http:
      valid_http_versions: ["HTTP/1.1", "HTTP/2.0"]
      valid_status_codes: [200, 201, 202]
      method: GET
      headers:
        User-Agent: "Custom-Agent/1.0"
```

#### VMAgent配置示例
```yaml
global:
  scrape_interval: 30s
  evaluation_interval: 30s

scrape_configs:
  - job_name: blackbox_http_http_default
    scrape_interval: 30s
    metrics_path: /probe
    params:
      module: [http_default]
    static_configs:
      - targets:
          - "https://example.com"
          - "https://api.service.com"
        labels:
          node_ip: "*************"
          node_city: "Beijing"
          node_isp: "China Telecom"
          prober: "http"
          module: "http_default"
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: 127.0.0.1:9115
```

### 3. 配置推送

#### 通过界面推送
1. 在配置查看窗口中点击"推送配置到节点"
2. 系统通过WebSocket将配置推送到目标节点

#### 通过API推送
```bash
# 推送配置到指定节点
curl -X POST "http://localhost:8080/ws/api/push/node" \
  -H "Content-Type: application/json" \
  -d '{"target_ip":"*************","message":"推送新配置"}'
```

## 🧪 测试验证

### 1. 功能测试
访问测试页面: `http://localhost:8080/web/test.html`

测试项目:
- Blackbox配置生成
- VMAgent配置生成  
- 所有配置生成
- 配置推送功能

### 2. 配置验证
```bash
# 启动服务
go run cmd/main.go

# 创建测试Item
curl -X POST "http://localhost:8080/api/items" \
  -H "Content-Type: application/json" \
  -d '{
    "target": "https://www.example.com",
    "method": "HTTP_GET",
    "owner": "test",
    "interval": 30,
    "status": 1,
    "item_data": "{\"headers\":{\"User-Agent\":\"Test-Agent\"}}"
  }'

# 生成配置
curl "http://localhost:8080/api/configs/generate/all?ip=127.0.0.1"
```

## 🔍 配置规则详解

### 1. 模块名称清理规则
目标URL清理规则:
- `://` → `_`
- `/` → `_`  
- `:` → `_`
- `?` → `_`
- `&` → `_`
- `=` → `_`
- 长度限制: 50字符

### 2. 默认配置模板
每种探测方法都有对应的默认配置:
- **HTTP**: 支持HTTP/1.1和HTTP/2.0，期望200状态码
- **TCP**: 使用IPv4协议
- **ICMP**: 使用IPv4协议  
- **DNS**: 查询A记录，期望NOERROR响应

### 3. 自定义配置检测
系统会检查Item的ItemData字段:
- 解析JSON配置
- 对比默认值
- 判断是否需要自定义模块

## 📈 性能优化

### 1. 配置缓存
- 模块配置按节点缓存
- 避免重复生成相同配置

### 2. 模板优化
- 使用embed嵌入模板文件
- 模板预编译提高性能

### 3. 批量处理
- 支持批量生成多节点配置
- 异步处理大量配置生成

## 🚀 部署说明

### 1. 依赖要求
- Go 1.19+
- 支持embed特性
- WebSocket支持

### 2. 配置文件
确保模板文件存在:
- `service/template/blackbox_exporter.tmpl`
- `service/template/vmagent.tmpl`

### 3. 启动验证
```bash
# 启动服务
go run cmd/main.go

# 验证接口
curl "http://localhost:8080/api/configs/generate/all?ip=127.0.0.1"
```

现在系统支持智能的Blackbox配置生成和自动的VMAgent配置生成，大大简化了监控配置的管理工作！🎉
