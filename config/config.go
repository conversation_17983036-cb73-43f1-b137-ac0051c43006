package config

import (
	"gopkg.in/yaml.v3"
	"io/ioutil"
	"log"
	"os"
	"sync"
)

// Config holds all configuration for the application
type Config struct {
	Database DatabaseConfig `yaml:"database"`
	Server   ServerConfig   `yaml:"server"`
	Files    FilesConfig    `yaml:"files"`
}

// DatabaseConfig holds database connection parameters
type DatabaseConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	DBName   string `yaml:"dbname"`
}

// ServerConfig holds server configuration
type ServerConfig struct {
	Port int `yaml:"port"`
}

// FilesConfig holds file paths configuration
type FilesConfig struct {
	DNSListPath string `yaml:"dns_list_path"`
	AWDBPath    string `yaml:"awdb_path"`
	PushPath    string `yaml:"push_path"`
}

var (
	config     *Config
	configOnce sync.Once
	configMu   sync.RWMutex
)

// LoadConfig loads configuration from config.yaml file (只加载一次)
func LoadConfig(customPath string) error {
	var err error
	configOnce.Do(func() {
		configPath := getConfigPath(customPath)
		data, readErr := ioutil.ReadFile(configPath)
		if readErr != nil {
			err = readErr
			return
		}

		config = &Config{}
		err = yaml.Unmarshal(data, config)
		if err != nil {
			log.Printf("Failed to parse config: %v", err)
		} else {
			log.Printf("Config loaded from: %s", configPath)
		}
	})
	return err
}

// GetDatabaseConfig returns database configuration
func GetDatabaseConfig(customPath string) *DatabaseConfig {
	err := LoadConfig(customPath)
	if err != nil {
		log.Printf("Failed to load config: %v, using defaults", err)
		return &DatabaseConfig{
			Host:     "localhost",
			Port:     3306,
			Username: "root",
			Password: "",
			DBName:   "boce",
		}
	}
	return &config.Database
}

func GetServerConfig(customPath string) *ServerConfig {
	err := LoadConfig(customPath)
	if err != nil {
		return &ServerConfig{
			Port: 8081,
		}
	}
	return &config.Server
}

// GetFilesConfig returns files configuration
func GetFilesConfig(customPath string) *FilesConfig {
	configMu.RLock()
	if config != nil {
		defer configMu.RUnlock()
		return &config.Files
	}
	configMu.RUnlock()

	// 如果配置未加载，则加载配置
	err := LoadConfig(customPath)
	if err != nil {
		log.Printf("Failed to load config: %v, using defaults", err)
		return &FilesConfig{
			DNSListPath: "dns_list",
			AWDBPath:    "ip_cn_v1.awdb",
			PushPath:    "data",
		}
	}

	configMu.RLock()
	defer configMu.RUnlock()
	return &config.Files
}

// Helper function to get config file path
func getConfigPath(customPath ...string) string {
	// 如果指定了自定义路径，优先使用
	if len(customPath) > 0 && customPath[0] != "" {
		if _, err := os.Stat(customPath[0]); err == nil {
			return customPath[0]
		} else {
			log.Printf("指定的配置文件不存在: %s, 使用默认路径", customPath[0])
		}
	}

	// 检查常见的配置文件位置
	configPaths := []string{
		"config.yaml",
		"conf/config.yaml",
		"config/config.yaml",
		"/etc/boce/config.yaml",
	}

	for _, path := range configPaths {
		if _, err := os.Stat(path); err == nil {
			return path
		}
	}

	// 默认返回 config.yaml
	return "config.yaml"
}
